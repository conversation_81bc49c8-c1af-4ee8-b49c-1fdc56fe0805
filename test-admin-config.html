<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>管理后台配置测试</title>
</head>
<body>
    <h1>管理后台配置测试</h1>
    <div id="config-info">
        <h2>当前环境变量：</h2>
        <p>VUE_APP_API_HOST: <span id="api-host"></span></p>
        <p>VUE_APP_API_PORT: <span id="api-port"></span></p>
        <p>VUE_APP_PROJECT_NAME: <span id="project-name"></span></p>
    </div>

    <div id="test-result">
        <h2>测试结果：</h2>
        <p id="result-message"></p>
    </div>

    <div id="instructions">
        <h2>解决方案：</h2>
        <ol>
            <li>确保Spring Boot应用已启动在 http://localhost:8080/shoppingonline/</li>
            <li>如果使用Vue开发服务器，请重新启动：
                <pre>cd src/main/resources/admin/admin
npm run serve</pre>
            </li>
            <li>如果直接访问打包后的文件，请确保访问：
                <pre>http://localhost:8080/shoppingonline/admin/dist/index.html</pre>
            </li>
            <li>检查浏览器开发者工具的Network标签，确认请求URL是否正确</li>
        </ol>
    </div>

    <script>
        // 模拟环境变量检查
        const envVars = {
            VUE_APP_API_HOST: 'localhost',
            VUE_APP_API_PORT: '8080',
            VUE_APP_PROJECT_NAME: 'shoppingonline'
        };

        document.getElementById('api-host').textContent = envVars.VUE_APP_API_HOST;
        document.getElementById('api-port').textContent = envVars.VUE_APP_API_PORT;
        document.getElementById('project-name').textContent = envVars.VUE_APP_PROJECT_NAME;

        // 检查配置
        if (envVars.VUE_APP_PROJECT_NAME === 'shoppingonline' && envVars.VUE_APP_API_HOST === 'localhost') {
            document.getElementById('result-message').innerHTML = 
                '<span style="color: green;">✓ 环境配置正确！</span>';
        } else {
            document.getElementById('result-message').innerHTML = 
                '<span style="color: red;">✗ 环境配置需要修改</span>';
        }

        // 测试API连接
        fetch('/shoppingonline/yonghu/login?username=test&password=test')
            .then(response => {
                if (response.status === 404) {
                    document.getElementById('result-message').innerHTML += 
                        '<br><span style="color: red;">✗ API路径错误 - 仍然使用旧路径</span>';
                } else {
                    document.getElementById('result-message').innerHTML += 
                        '<br><span style="color: green;">✓ API路径正确</span>';
                }
            })
            .catch(error => {
                document.getElementById('result-message').innerHTML += 
                    '<br><span style="color: orange;">? 无法测试API连接 - 请确保后端已启动</span>';
            });
    </script>
</body>
</html>

@echo off
echo Starting Spring Boot Application with optimized memory settings...
echo.

REM Set JVM memory parameters to reduce memory usage
set JAVA_OPTS=-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC -XX:G1HeapRegionSize=16m

REM Check if Maven is available
where mvn >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Maven not found in PATH. Please install Maven or add it to your PATH.
    pause
    exit /b 1
)

echo Compiling and starting the application...
echo Using JVM options: %JAVA_OPTS%
echo.

REM Start the Spring Boot application with memory optimization
mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

pause

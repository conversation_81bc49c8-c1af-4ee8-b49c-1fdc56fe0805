<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>注册</title>
		<link rel="stylesheet" type="text/css" href="../../layui/css/layui.css">
	    <link rel="stylesheet" type="text/css" href="../../xznstatic/css/public.css"/>
	    <link rel="stylesheet" type="text/css" href="../../xznstatic/css/login.css"/>
	    <style type="text/css">
			.register {
				display: flex;
				justify-content: center;
				align-items: center;
			    width: 100%;
			    height: 100%;
				background-attachment: fixed;
				background-size: cover;
				background-position: center;
								background-image: url(http://codegen.caihongy.cn/20201206/eaa69c2b4fa742f2b5acefd921a772fc.jpg);
							}
			.register form {
				box-sizing: border-box;
				min-height: 400px;
				display: flex;
				flex-direction: column;
				justify-content: center !important;
			}
			.register .logo, .register .title {
				box-sizing: border-box;
			}
			.register .logo img {
				display: block;
			}
			.register .title {
				text-align: center;
			}
			.register .form-item {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				box-sizing: border-box;
			}
			.register .form-item input, .register .form-label {
				box-sizing: border-box;
			}
			.register .btn-submit {
				display: block;
				box-sizing: border-box;
			}
			.register form p.txt {
				width: 100%;
				margin: 0;
				box-sizing: border-box;
			}
	    </style>
	</head>
	<body>
		<div class="register" id="app">
			<form class="layui-form login-form" :style='{"padding":"20px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(0, 0, 0, 0)","backgroundColor":"rgba(255, 255, 255, 0.48)","borderRadius":"20px","borderWidth":"0","width":"450px","borderStyle":"solid","justifyContent":"center","height":"auto"}'>
				<h1 class="logo" v-if="false" :style='{"padding":"5px 0","boxShadow":"0 0 6px rgba(255,0,0,.8)","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","borderRadius":"6px","borderWidth":"0","borderStyle":"solid"}'><img :style='{"boxShadow":"0 0 6px rgba(255,0,0,.8)","margin":"0 auto","borderColor":"rgba(0,0,0,.3)","borderRadius":"100%","borderWidth":"1px","width":"44px","borderStyle":"solid","height":"44px"}' src="http://codegen.caihongy.cn/20201024/ed5e326ca66f403aa3197b5fbb4ec733.jpg"></h1>
            	<p class="title" v-if="true" :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px auto","borderColor":"rgba(0,0,0,1)","backgroundColor":"rgba(247, 247, 247, 0)","color":"red","isshow":true,"borderRadius":"8px","borderWidth":"0","width":"110px","lineHeight":"40px","fontSize":"18px","borderStyle":"solid"}'>用户注册</p>		
				<div :style='{"padding":"0","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"0 auto","borderColor":"rgba(0,0,0,1)","backgroundColor":"rgba(255, 255, 255, 0)","borderRadius":"0","borderWidth":"0 0 1px 0","width":"80%","borderStyle":"solid","height":"64px"}' class="form-item layui-form-text">
					<label v-if="true" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(46, 78, 126, 1)","borderRadius":"0","textAlign":"left","borderWidth":"0","width":"125px","fontSize":"16px","borderStyle":"solid"}' class="form-label">用户名：</label>
					<input :style='{"padding":"0 10px","boxShadow":"3px 3px 0px #a1afc9","borderColor":"rgba(46, 78, 126, 1)","backgroundColor":"rgba(255, 255, 255, 0.46)","color":"rgba(203, 58, 86, 1)","borderRadius":"6px","textAlign":"left","borderWidth":"1px","width":"200px","fontSize":"14px","borderStyle":"solid","height":"50px"}' type="text" id="yonghuming" name="yonghuming" placeholder="请输入用户名" autocomplete="off" class="layui-input">
				</div>
				<div :style='{"padding":"0","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"0 auto","borderColor":"rgba(0,0,0,1)","backgroundColor":"rgba(255, 255, 255, 0)","borderRadius":"0","borderWidth":"0 0 1px 0","width":"80%","borderStyle":"solid","height":"64px"}' class="form-item">
					<label v-if="true" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(46, 78, 126, 1)","borderRadius":"0","textAlign":"left","borderWidth":"0","width":"125px","fontSize":"16px","borderStyle":"solid"}' class="form-label">密码：</label>
					<input :style='{"padding":"0 10px","boxShadow":"3px 3px 0px #a1afc9","borderColor":"rgba(46, 78, 126, 1)","backgroundColor":"rgba(255, 255, 255, 0.46)","color":"rgba(203, 58, 86, 1)","borderRadius":"6px","textAlign":"left","borderWidth":"1px","width":"200px","fontSize":"14px","borderStyle":"solid","height":"50px"}' type="password" name="mima" required lay-verify="required" placeholder="请输入密码" autocomplete="off" class="layui-input">
				</div>
				<div :style='{"padding":"0","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"0 auto","borderColor":"rgba(0,0,0,1)","backgroundColor":"rgba(255, 255, 255, 0)","borderRadius":"0","borderWidth":"0 0 1px 0","width":"80%","borderStyle":"solid","height":"64px"}' class="form-item layui-form-text">
					<label v-if="true" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(46, 78, 126, 1)","borderRadius":"0","textAlign":"left","borderWidth":"0","width":"125px","fontSize":"16px","borderStyle":"solid"}' class="form-label">姓名：</label>
					<input :style='{"padding":"0 10px","boxShadow":"3px 3px 0px #a1afc9","borderColor":"rgba(46, 78, 126, 1)","backgroundColor":"rgba(255, 255, 255, 0.46)","color":"rgba(203, 58, 86, 1)","borderRadius":"6px","textAlign":"left","borderWidth":"1px","width":"200px","fontSize":"14px","borderStyle":"solid","height":"50px"}' type="text" id="xingming" name="xingming" placeholder="请输入姓名" autocomplete="off" class="layui-input">
				</div>
				<div :style='{"padding":"0","boxShadow":"0 0 0px rgba(255,0,0,0)","margin":"0 auto","borderColor":"rgba(0,0,0,1)","backgroundColor":"rgba(255, 255, 255, 0)","borderRadius":"0","borderWidth":"0 0 1px 0","width":"80%","borderStyle":"solid","height":"64px"}' class="form-item layui-form-text">
					<label v-if="true" :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"transparent","color":"rgba(46, 78, 126, 1)","borderRadius":"0","textAlign":"left","borderWidth":"0","width":"125px","fontSize":"16px","borderStyle":"solid"}' class="form-label">联系电话：</label>
					<input :style='{"padding":"0 10px","boxShadow":"3px 3px 0px #a1afc9","borderColor":"rgba(46, 78, 126, 1)","backgroundColor":"rgba(255, 255, 255, 0.46)","color":"rgba(203, 58, 86, 1)","borderRadius":"6px","textAlign":"left","borderWidth":"1px","width":"200px","fontSize":"14px","borderStyle":"solid","height":"50px"}' type="text" id="lianxidianhua" name="lianxidianhua" placeholder="请输入联系电话" autocomplete="off" class="layui-input">
				</div>
				<button :style='{"padding":"0 10px","boxShadow":"0 0px 0px #4c8dae","margin":"10px auto","borderColor":"#ccc","backgroundColor":"rgba(76, 141, 174, 1)","color":"rgba(234, 205, 118, 1)","borderRadius":"8px","borderWidth":"0","width":"60%","fontSize":"14px","lineHeight":"","borderStyle":"solid","height":"44px"}' class="layui-btn layui-btn-fluid layui-btn-danger btn-submit" lay-submit lay-filter="register" style="width: 280px;">注册</button>
            	<p :style='{"color":"rgba(68, 206, 246, 1)","textAlign":"right","fontSize":"14px"}' class="txt"><a href="javascript:window.location.href='../login/login.html'">已有账号登录</a></p>
			</form>
		</div>

		<script src="../../layui/layui.js"></script>
		<script src="../../js/vue.js"></script>
		<!-- 组件配置信息 -->
		<script src="../../js/config.js"></script>
		<!-- 扩展插件配置信息 -->
		<script src="../../modules/config.js"></script>
		<!-- 工具方法 -->
		<script src="../../js/utils.js"></script>
		<!-- 校验格式工具类 -->
		<script src="../../js/validate.js"></script>

		<script>
			var vue = new Vue({
			  el: '#app',
			  data: {
			    
			  },
			  methods: {
			    
			  }
			});
			
			layui.use(['layer', 'element', 'carousel', 'form', 'http', 'jquery'], function() {
				var layer = layui.layer;
				var element = layui.element;
				var carousel = layui.carousel;
				var form = layui.form;
				var http = layui.http;
				var jquery = layui.jquery;

				var tablename = http.getParam('tablename');
								
				// 注册
				form.on('submit(register)', function(data) {
					data = data.field;
                    					                    if(!data.yonghuming){
                        layer.msg('用户名不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                                    					                    if(!data.mima){
                        layer.msg('密码不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                                    					                                                                                                                                                                					                                                                                                                                                                					                                                                                                                                                                					                                                                                if(!isMobile(data.lianxidianhua)){
                        layer.msg('联系电话应输入手机格式', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                        					                                                                                                                                                                                    http.requestJson(tablename + '/register', 'post', data, function(res) {
						layer.msg('注册成功', {
							time: 2000,
							icon: 6
						},function(){
							// 路径访问设置
							window.location.href = '../login/login.html';
						});
					});
					return false
				});
			});
		</script>
	</body>
</html>

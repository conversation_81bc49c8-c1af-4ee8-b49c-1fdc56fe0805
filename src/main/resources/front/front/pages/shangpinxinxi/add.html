
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>注册</title>
		<!-- bootstrap样式，地图插件使用 -->
		<link rel="stylesheet" href="../../css/bootstrap.min.css" />
		<link rel="stylesheet" href="../../layui/css/layui.css">
		<!-- 样式 -->
		<link rel="stylesheet" href="../../css/style.css" />
		<!-- 主题（主要颜色设置） -->
		<link rel="stylesheet" href="../../css/theme.css" />
		<!-- 通用的css -->
		<link rel="stylesheet" href="../../css/common.css" />
	</head>
	<style>
		html::after {
			position: fixed;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			content: '';
			display: block;
			background-attachment: fixed;
			background-size: cover;
			background-position: center;
						z-index: -1;
		}
		#swiper {
			overflow: hidden;
			margin: 0 auto;
		}
		#swiper .layui-carousel-ind li {
			width: 16px;
			height: 4px;
			border-width: 0;
			border-style: solid;
			border-color: rgba(0,0,0,.3);
			border-radius: 0px;
			background-color: rgba(255, 255, 255, 1);
			box-shadow: 0 0 0px rgba(255,0,0,.8);
		}
		#swiper .layui-carousel-ind li.layui-this {
			width: 16px;
			height: 4px;
			border-width: 0;
			border-style: solid;
			border-color: rgba(0,0,0,.3);
			border-radius: 0px;
			background-color: rgba(0, 85, 119, 1);
			box-shadow: 0 0 0px rgba(0, 85, 119, 1);
		}
		
		button, button:focus {
			outline: none;
		}
		
		.data-add-container .add .layui-select-title .layui-unselect {
			padding: 0 12px;
			height: 50px;
			font-size: 16px;
			color: #333;
			border-radius: 4px;
			border-width: 1px;
			border-style: solid;
			border-color: rgba(107, 104, 130, 1);
			background-color: #fff;
			box-shadow: 0 0 0px rgba(255,0,0,.8);
			text-align: left;
		}
		.data-add-container .add .layui-form-item {
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.data-add-container .layui-form-pane .layui-form-item[pane] .layui-form-label {
			margin: 0;
			position: inherit;
			background: transparent;
			border: 0;
		}
		.data-add-container .add .layui-input-block {
			margin: 0;
			flex: 1;
		}
		.data-add-container .layui-form-pane .layui-form-item[pane] .layui-input-inline {
			margin: 0;
			flex: 1;
			display: flex;
		}
	</style>
	<body style="background: #EEEEEE; ">


		<div id="app">

			<!--
			<div class="layui-carousel" id="swiper">
				<div carousel-item id="swiper-item">
					<div v-for="(item,index) in swiperList" v-bind:key="index">
						<img class="swiper-item" :src="item.img">
					</div>
				</div>
			</div> -->
			<div class="layui-carousel" id="swiper" :style='{"boxShadow":"0 0px 0px #fff143","margin":"0 ","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
			  <div carousel-item id="swiper-item">
				<div v-for="(item,index) in swiperList" :key="index">
					<img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" />
				</div>
			  </div>
			</div>
			<!-- 轮播图 -->

			<div class="data-add-container" :style='{"padding":"20px","boxShadow":"0px 0px 0px rgba(255,0,0,.8)","margin":"50px auto","borderColor":"rgba(255, 0, 0, 0)","backgroundColor":"#fff","borderRadius":"10px","borderWidth":"0","borderStyle":"solid"}'>

				<form class="layui-form layui-form-pane add" lay-filter="myForm">
					
                                                            <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">商品名称：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.shangpinmingcheng" type="text" :readonly="ro.shangpinmingcheng" name="shangpinmingcheng" id="shangpinmingcheng" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
                        <label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">商品分类：</label>
                        <div class="layui-input-block">
                            <select name="shangpinfenlei" id="shangpinfenlei" lay-filter="shangpinfenlei">
                                <option value="">请选择</option>
                                <option v-for="(item,index) in shangpinfenlei" v-bind:key="index" :value="item">{{item}}</option>
                            </select>
                        </div>
                    </div>
                                                                                <div :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">图片：</label>
						<div class="layui-input-block">
							<div v-if="detail.tupian" style="display:inline-block;margin-right:10px;">
								<img id="tupianImg" style="width: 100px;height: 100px;border-radius: 50%;border: 2px solid #EEEEEE;" :src="detail.tupian">
								<input type="hidden" :value="detail.tupian" id="tupian" name="tupian" />
							</div>
							<button v-if="!ro.tupian" :style='{"padding":"0 10px","boxShadow":"3px 3px 0px  #e0eee8","margin":"0 10px 0 0","borderColor":"#ccc","backgroundColor":"rgba(164, 226, 198, 1)","color":"rgba(157, 41, 51, 1)","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"44px"}' type="button" class="layui-btn btn-theme" id="tupianUpload">
								<i v-if="true" :style='{"color":"rgba(157, 41, 51, 1)","show":true,"fontSize":"14px"}' class="layui-icon">&#xe67c;</i>上传图片
							</button>
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">品牌：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.pinpai" type="text" :readonly="ro.pinpai" name="pinpai" id="pinpai" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">规格：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.guige" type="text" :readonly="ro.guige" name="guige" id="guige" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                                                                            <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">最近点击时间：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' type="text" name="clicktime" id="clicktime" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">点击次数：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.clicknum" type="text" :readonly="ro.clicknum" name="clicknum" id="clicknum" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">价格：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.price" type="text" :readonly="ro.price" name="price" id="price" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                            
                                                                                                                                                                                                                                                                    <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">商品详情：</label>
						<div class="layui-input-block">
							<textarea name="shangpinxiangqing" id="shangpinxiangqing">请输入商品详情</textarea>
						</div>
					</div>
                                                                                                                                                                
					<div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item">
						<div class="layui-input-block" style="text-align: right;">
							<button :style='{"padding":"0 10px","boxShadow":"8px 6px 6px #a1afc9","margin":"0 10px","borderColor":"#ccc","backgroundColor":"rgba(23, 124, 176, 1)","color":"#fff","borderRadius":"6px","borderWidth":"0","width":"25%","fontSize":"16px","borderStyle":"solid","height":"50px"}' class="layui-btn btn-submit" lay-submit lay-filter="*">提交</button>
							<button :style='{"padding":"0 10px","boxShadow":"6px 5px 6px #c2ccd0","margin":"0 10px","borderColor":"#ccc","backgroundColor":"rgba(214, 236, 240, 1)","color":"rgba(219, 90, 107, 1)","borderRadius":"6px","borderWidth":"0","width":"25%","fontSize":"16px","borderStyle":"solid","height":"50px"}' type="reset" class="layui-btn layui-btn-primary">重置</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<script src="../../layui/layui.js"></script>
		<script src="../../js/vue.js"></script>
		<!-- 组件配置信息 -->
		<script src="../../js/config.js"></script>
		<!-- 扩展插件配置信息 -->
		<script src="../../modules/config.js"></script>
		<!-- 工具方法 -->
		<script src="../../js/utils.js"></script>
		<!-- 校验格式工具类 -->
		<script src="../../js/validate.js"></script>
		<!-- 地图 -->
		<script type="text/javascript" src="../../js/jquery.js"></script>
		<script type="text/javascript" src="http://webapi.amap.com/maps?v=1.3&key=ca04cee7ac952691aa67a131e6f0cee0"></script>
		<script type="text/javascript" src="../../js/bootstrap.min.js"></script>
		<script type="text/javascript" src="../../js/bootstrap.AMapPositionPicker.js"></script>

		<script>
			var jquery = $;
			
			var vue = new Vue({
				el: '#app',
				data: {
					// 轮播图
					swiperList: [{
						img: '../../img/banner.jpg'
					}],
					dataList: [],
      					ro:{
				        shangpinmingcheng : false,
				        shangpinfenlei : false,
				        tupian : false,
				        pinpai : false,
				        guige : false,
				        shangpinxiangqing : false,
				        clicktime : false,
				        clicknum : false,
				        price : false,
              				},
                    detail: {
                                                                        shangpinmingcheng: '',
                                                                                                shangpinfenlei: '',
                                                                                                tupian: '',
                                                                                                pinpai: '',
                                                                                                guige: '',
                                                                                                shangpinxiangqing: '',
                                                                                                clicktime: '',
                                                                                                clicknum: '',
                                                                                                price: '',
                                                                    },
                    																				shangpinfenlei: [],
																																																																																					centerMenu: centerMenu
				},
				updated: function() {
					layui.form.render('select', 'myForm');
				},
                computed: {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                },
				methods: {
					jump(url) {
						jump(url)
					}
				}
			})

			
			layui.use(['layer', 'element', 'carousel', 'http', 'jquery', 'form', 'upload', 'laydate','tinymce'], function() {
				var layer = layui.layer;
				var element = layui.element;
				var carousel = layui.carousel;
				var http = layui.http;
				var jquery = layui.jquery;
				var form = layui.form;
				var upload = layui.upload;
				var laydate = layui.laydate;
                var tinymce = layui.tinymce

				// 获取轮播图 数据
				http.request('config/list', 'get', {
					page: 1,
					limit: 5
				}, function(res) {
					if (res.data.list.length > 0) {
						var swiperItemHtml = '';
						for (let item of res.data.list) {
							if (item.name.indexOf('picture') >= 0 && item.value && item.value != "" && item.value != null) {
								swiperItemHtml +=
									'<div>' +
									'<img style="width: 100%;height: 100%;object-fit:cover;" class="swiper-item" src="' + item.value + '">' +
									'</div>';
							}
						}
						jquery('#swiper-item').html(swiperItemHtml);
						// 轮播图
						vue.$nextTick(() => {
						  carousel.render({
						  	elem: '#swiper',
							width: '100%',
						  	height: '600px',
						  	arrow: 'hover',
						  	anim: 'default',
						  	autoplay: 'true',
						  	interval: '3000',
						  	indicator: 'inside'
						  });
						
						})
						// carousel.render({
						// 	elem: '#swiper',
						// 	width: swiper.width,height:swiper.height,
						// 	arrow: swiper.arrow,
						// 	anim: swiper.anim,
						// 	interval: swiper.interval,
						// 	indicator: "none"
						// });
					}
				});

                																http.request(`option/shangpinfenlei/shangpinfenlei`,'get',{},(res)=>{
					vue.shangpinfenlei = res.data
				});
																// 上传图片
				var tupianUpload = upload.render({
					//绑定元素
					elem: '#tupianUpload',
					//上传接口
					url: http.baseurl + 'file/upload',
					// 请求头
					headers: {
						Token: localStorage.getItem('Token')
					},
					// 允许上传时校验的文件类型
					accept: 'images',
					before: function() {
						//loading层
						var index = layer.load(1, {
							shade: [0.1, '#fff'] //0.1透明度的白色背景
						});
					},
					// 上传成功
					done: function(res) {
						console.log(res);
						layer.closeAll();
						if (res.code == 0) {
							layer.msg("上传成功", {
								time: 2000,
								icon: 6
							})
							var url = http.baseurl + 'upload/' + res.file;
							jquery('#tupian').val(url);
							jquery('#tupianImg').attr('src', url)
                            vue.detail.tupian = url;
						} else {
							layer.msg(res.msg, {
								time: 2000,
								icon: 5
							})
						}
					},
					//请求异常回调
					error: function() {
						layer.closeAll();
						layer.msg("请求接口异常", {
							time: 2000,
							icon: 5
						})
					}
				});
																												                var edit = tinymce.render({
                    elem: "#shangpinxiangqing",
                    height: 600,
                    images_upload_handler: function(blobInfo, succFun, failFun) {
                        var xhr, formData;
                        var file = blobInfo.blob(); //转化为易于理解的file对象
                        xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open('POST', http.baseurl + 'file/upload');
                        xhr.setRequestHeader("Token", localStorage.getItem('Token')); //设置请求头
                        xhr.onload = function() {
                            var json;
                            if (xhr.status != 200) {
                                failFun('HTTP Error: ' + xhr.status);
                                return;
                            }
                            json = JSON.parse(xhr.responseText);
                            if (!json || typeof json.file != 'string') {
                                failFun('Invalid JSON: ' + xhr.responseText);
                                return;
                            }
                            succFun(http.baseurl + 'upload/' + json.file);
                        };
                        formData = new FormData();
                        formData.append('file', file, file.name); //此处与源文档不一样
                        xhr.send(formData);
                    }
                }, (opt) => {

                });
																laydate.render({
					elem: '#clicktime',
					type: 'datetime'
				});
                																								
                // session独取
				let table = localStorage.getItem("userTable");
				http.request(`${table}/session`, 'get', {}, function(data) {
					// 表单赋值
					//form.val("myForm", data.data);
					data = data.data
					for (var key in data){
                                        }
				});

                // 跨表
                if(http.getParam('corss')){
					var obj = JSON.parse(localStorage.getItem('crossObj'));
					for (var o in obj){
					if(o=='shangpinmingcheng'){
          	                                vue.detail[o] = obj[o];
						vue.ro.shangpinmingcheng = true;
						continue;
                                        }
					if(o=='shangpinfenlei'){
          	                                vue.detail[o] = obj[o];
						vue.ro.shangpinfenlei = true;
						continue;
                                        }
					if(o=='tupian'){
          	                                vue.detail[o] = obj[o];
						vue.ro.tupian = true;
						continue;
                                        }
					if(o=='pinpai'){
          	                                vue.detail[o] = obj[o];
						vue.ro.pinpai = true;
						continue;
                                        }
					if(o=='guige'){
          	                                vue.detail[o] = obj[o];
						vue.ro.guige = true;
						continue;
                                        }
					if(o=='shangpinxiangqing'){
          	                                vue.detail[o] = obj[o];
						vue.ro.shangpinxiangqing = true;
						continue;
                                        }
					if(o=='clicktime'){
          	                                vue.detail[o] = obj[o];
						vue.ro.clicktime = true;
						continue;
                                        }
					if(o=='clicknum'){
          	                                vue.detail[o] = obj[o];
						vue.ro.clicknum = true;
						continue;
                                        }
					if(o=='price'){
          	                                vue.detail[o] = obj[o];
						vue.ro.price = true;
						continue;
                                        }
                                        }
				}
				

				// 提交
				form.on('submit(*)', function(data) {
					data = data.field;
					
                    // 数据校验
                    if(!data.shangpinmingcheng){
                        layer.msg('商品名称不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                                    if(!data.shangpinfenlei){
                        layer.msg('商品分类不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    					var shangpinxiangqing = tinymce.get('#shangpinxiangqing').getContent()
					data.shangpinxiangqing = shangpinxiangqing;
                                                                                                                                                                                                                                                                                                                                if(!isIntNumer(data.clicknum)){
                        layer.msg('点击次数应输入整数', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                if(!data.price){
                        layer.msg('价格不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                if(!isNumber(data.price)){
                        layer.msg('价格应输入数字', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                        
					// 跨表计算
					                                                                                                                                                                                                                                                                                                                                                                        					
                    // 比较大小
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                					
					// 提交数据
					http.requestJson('shangpinxinxi' + '/add', 'post', data, function(res) {
					 	layer.msg('提交成功', {
					 		time: 2000,
					 		icon: 6
					 	}, function() {
					 		back();
						});
					 });

					return false
				});

			});
		</script>
	</body>
</html>


<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<title>首页</title>
<meta name="description" content="" />
<meta name="keywords" content="" />
<meta name="author" content="order by mobanxiu.cn" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<link rel="stylesheet" href="../../layui/css/layui.css">
<link href="https://cdn.bootcdn.net/ajax/libs/Swiper/5.4.5/css/swiper.min.css" rel="stylesheet">
<link rel="stylesheet" href="../../xznstatic/css/common.css"/>
<link rel="stylesheet" href="../../xznstatic/css/style.css"/>
<script src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
<script src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
</head>
<style>
	html::after {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		content: '';
		display: block;
		background-attachment: fixed;
		background-size: cover;
		background-position: center;
			}
	#test1 {
		overflow: hidden;
	}
	#test1 .layui-carousel-ind li {
		width: 16px;
		height: 4px;
		border-width: 0;
		border-style: solid;
		border-color: rgba(0,0,0,.3);
		border-radius: 0px;
		background-color: rgba(255, 255, 255, 1);
		box-shadow: 0 0 0px rgba(255,0,0,.8);
	}
	#test1 .layui-carousel-ind li.layui-this {
		width: 16px;
		height: 4px;
		border-width: 0;
		border-style: solid;
		border-color: rgba(0,0,0,.3);
		border-radius: 0px;
		background-color: rgba(0, 85, 119, 1);
		box-shadow: 0 0 0px rgba(0, 85, 119, 1);
	}

	.recommend {
	  padding: 10px 0;
	  display: flex;
	  justify-content: center;
	  background-repeat: no-repeat;
	  background-position: center center;
	  background-size: cover;
	}

	.recommend .box {
	    width: 1014px;
	}

	.recommend .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.recommend .box .title span {
		padding: 0 10px;
		line-height: 1.4;
	}

	.recommend .box .list {
		display: flex;
		flex-wrap: wrap;
	}
    	.index-pv1 .box .list .list-item {
		flex: 0 0 20%;
		padding: 0 5px;
		box-sizing: border-box;
	}

	.recommend .box .list .list-item-body {
		border: 1px solid rgba(0, 0, 0, 3);
		padding: 5px;
		box-sizing: border-box;
		cursor: pointer;
	}

	.recommend .box .list img {
		width: 100%;
		height: 100px;
		display: block;
		margin: 0 auto;
    object-fit: cover;
    max-width: 100%;
	}

	.recommend .box .list .name {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		text-align: center;
		box-sizing: border-box;
	}

	.recommend .box .list .list-item3 {
		flex: 0 0 33.33%;
	}

	.recommend .box .list .list-item5 {
		flex: 0 0 20%;
	}
	
	/* 商品推荐-样式4-开始 */
	.recommend .list-4{
	  display: flex;
	  flex-wrap: wrap;
	  justify-content: center;
	}
	.recommend .list-4 .list-4-body {
	    display: flex;
	    flex-direction: column;
	}
	
	.recommend .list-4 .list-4-item {
	    position: relative;
	    z-index: 1;
	}
	.recommend .list-4 .list-4-item.item-1 {
	    width: 400px;
	    height: 400px;
	    margin-right: 20px;
	}
	
	.recommend .list-4 .list-4-item.item-2 {
	    width: 220px;
	    height: 190px;
	    margin-right: 20px;
	    margin-bottom: 20px;
	}
	
	.recommend .list-4 .list-4-item.item-3 {
	    width: 220px;
	    height: 190px;
	    margin-right: 20px;
	    margin-bottom: 0;
	}
	
	.recommend .list-4 .list-4-item.item-4 {
	    width: 190px;
	    height: 190px;
	    margin-right: 0;
	    margin-bottom: 20px;
	}
	
	.recommend .list-4 .list-4-item.item-5 {
	    width: 190px;
	    height: 190px;
	    margin-right: 0;
	    margin-bottom: 0;
	}
	
	.recommend .list-4 .list-4-item img {
	      width: 100%;
	      height: 100%;
	      object-fit: cover;
	      display: block;
	    }
	
	.recommend .list-4 .list-4-item .list-4-item-center {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: auto;
		display: flex;
		flex-wrap: wrap;
		background-color: rgba(0,0,0,.3);
	}
	.recommend .list-4 .list-4-item .list-4-item-center .list-4-item-title {
		width: 50%;
		text-align: left;
		line-height: 44px;
		color: #fff;
		font-size: 14px;
		padding: 0 6px;
	}
	
	.recommend .list-4 .list-4-item .list-4-item-center .list-4-item-price {
		width: 50%;
		text-align: right;
		line-height: 44px;
		color: #fff;
		font-size: 14px;
		padding: 0 6px;
	}
	/* 商品推荐-样式4-结束 */
	/* 商品推荐-样式5-开始 */
	.recommend #recommend-five-swiper.swiper-container-horizontal>.swiper-pagination-bullets {
	    line-height: 1;
	}
	.recommend #recommend-five-swiper .swiper-slide.swiper-slide-prev {
		z-index: 5;
	}
	.recommend #recommend-five-swiper .swiper-slide.swiper-slide-next {
		z-index: 5;
	}
	.recommend #recommend-five-swiper .swiper-slide.swiper-slide-active {
		z-index: 9;
	}
	
	.recommend #lists-five-swiper.swiper-container-horizontal>.swiper-pagination-bullets {
	    line-height: 1;
	}
	.recommend #lists-five-swiper .swiper-slide.swiper-slide-prev {
		z-index: 5;
	}
	.recommend #lists-five-swiper .swiper-slide.swiper-slide-next {
		z-index: 5;
	}
	.recommend #lists-five-swiper .swiper-slide.swiper-slide-active {
		z-index: 9;
	}
	/* 商品推荐-样式5-结束 */

	.news {
		padding: 10px 0;
		display: flex;
		justify-content: center;
		background-repeat: no-repeat;
		background-position: center center;
		background-size: cover;
		width: 100%;
	}

	.news .box {
	    width: 1014px;
	}

	.news .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.news .box .title span {
		padding: 0 10px;
		line-height: 1.4;
	}

	.news .box .list {
		display: flex;
		flex-wrap: wrap;
	}
      	.index-pv2 .box .list .list-item {
		flex: 0 0 50%;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.news .box .list .list-item .list-item-body {
		border: 1px solid rgba(0, 0, 0, 3);
		padding: 10px;
		box-sizing: border-box;
		display: flex;
		cursor: pointer;
	}

	.news .box .list .list-item .list-item-body img {
		width: 120px;
		height: 100%;
		display: block;
		margin: 0 auto;
    object-fit: cover;
    max-width: 100%;
	}

	.news .box .list .list-item .list-item-body .item-info {
		flex: 1;
		display: flex;
		justify-content: space-between;
		flex-direction: column;
		padding-left: 10px;
		box-sizing: border-box;
	}

	.news .box .list .list-item .list-item-body .item-info .name {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		box-sizing: border-box;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.news .box .list .list-item .list-item-body .item-info .time {
		padding-top: 5px;
		color: red;
		font-size: 14px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.news .box .list .list-item1 {
		flex: 0 0 100%;
	}

	.news .box .list .list-item3 {
		flex: 0 0 33.33%;
	}

	.lists {
		padding: 10px 0;
		display: flex;
		justify-content: center;
		background-repeat: no-repeat;
		background-position: center center;
		background-size: cover;
	}

	.lists .box {
	    width: 1014px;
	    overflow: hidden;
	}

	.lists .box .title {
		padding: 10px 5px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.lists .box .title span {
		padding: 0 10px;
		line-height: 1.4;
	}

	.lists .box .swiper-slide {
		box-sizing: border-box;
		cursor: pointer;
	}

	.lists .box .swiper-slide .img-box {
		width: 100%;
		overflow: hidden;
	}

	.lists .box .swiper-slide .img-box img {
		width: 100%;
		height: 100%;
		object-fit: cover;
    max-width: 100%;
	}

            	.index-pv3 .box .list .list-item {
  		flex: 0 0 25%;
  		padding: 0 10px;
  		box-sizing: border-box;
  	}

	.index-pv1 .animation-box:hover {
		transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1.1) rotate(0deg) skew(0deg, 0deg);
		transition: all 0.4s;
		z-index: 2;
		position: relative;
	}
  .index-pv2 .animation-box:hover {
  	transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1.0) rotate(0deg) skew(0deg, 0deg);
  	transition: all 0.3s;
	z-index: 2;
	position: relative;
  }
  .index-pv3 .animation-box:hover {
  	transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(0.95) rotate(0deg) skew(0deg, 0deg);
  	transition: all 0.3s;
	z-index: 2;
	position: relative;
  }
  
	#new-list-6 .swiper-wrapper {
		-webkit-transition-timing-function: linear;
		-moz-transition-timing-function: linear;
		-o-transition-timing-function: linear;
		transition-timing-function: linear;
	}
</style>
<body>
  <div id="app">
    <div class="banner">
		<div class="layui-carousel" id="test1" :style='{"boxShadow":"0 0px 0px #fff143","margin":"0 ","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
		  <div carousel-item>
			<div v-for="(item,index) in swiperList" :key="index">
				<img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" alt="轮播图" />
			</div>
		  </div>
		</div>
      <!-- <div class="bd">
        <ul>
          <li v-for="(item,index) in swiperList" v-bind:key="index"><img style="width: 1920px;height: 492px;" :src="item.img" /></li>
        </ul>
        <div class="hd">
          <ul>
            <li v-for="(item,index) in swiperList" v-bind:key="index"></li>
          </ul>
        </div>
      </div> -->
    </div>

	<div :style='{"display": "flex","justify-content": "center","padding":"20px"}'>
		<label for="queryName" style="display: none;">查询类型</label>
		<select v-if="queryList.length>1" @change="queryChange($event)" name="queryName" id="queryName">
			<option v-for="(item,index) in queryList" v-bind:key="index" :value="index">{{item.queryName}}</option>
		</select>
		<div v-if="queryIndex===0" class="item-list">
			<label for="shangpinxinxishangpinmingcheng" style="display: none;">商品名称</label>
			<input type="text" :style='{"boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(102, 93, 93, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"6px","textAlign":"center","borderWidth":"1px","width":"150px","fontSize":"14px","borderStyle":"solid","height":"40px"}'
			 name="shangpinxinxishangpinmingcheng" id="shangpinxinxishangpinmingcheng" placeholder="商品名称" autocomplete="off" class="layui-input">
		</div>
		<button v-if="queryIndex===0" :style='{"padding":"0 15px","boxShadow":"0 0 8px rgba(0,0,0,0)","margin":"0 0 0 10px","borderColor":"#409EFF","backgroundColor":"rgba(23, 124, 176, 1)","color":"#fff","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"18px","borderStyle":"solid","height":"40px"}'
		 id="btn-search" @click="jump('../shangpinxinxi/list.html')" type="button" class="layui-btn layui-btn-normal">
			<i v-if="true" class="layui-icon layui-icon-search"></i>搜索
		</button>
	</div>

    <div id="content">
	<!-- 商品推荐 -->
	<div class="recommend index-pv1" :style='{"padding":"0px  10px 0","boxShadow":"0 0 0px ","margin":"0px 0px ","borderColor":"rgba(255, 255, 255, 0)","backgroundColor":"rgba(252, 252, 252, 1)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
	  <div class="box" style='width:88%'>
	    <div class="title" :style='{"padding":"0px 0px 0","boxShadow":"0 0 0px ","margin":"10px 0 10px 0","borderColor":"rgba(115, 115, 185, 1)","backgroundColor":"rgba(234, 224, 224, 0)","color":"rgba(15, 15, 16, 1)","borderRadius":"2px","alignItems":"center","borderWidth":"0px 0px 0px 0px","fontSize":"20px","borderStyle":"solid"}'>
	      <span>Recommend</span><span>商品信息推荐</span>
	    </div>
		<div class="list-4">
		    <div v-if="shangpinxinxiRecommend.length>0" class="list-4-item animation-box item-1" @click="jump('../shangpinxinxi/detail.html?id='+shangpinxinxiRecommend[0].id)" :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0, .3)","margin":"0 20px 0 0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"400px","borderStyle":"solid","height":"400px"}'>
																																										<img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}' :src="shangpinxinxiRecommend[0].tupian?shangpinxinxiRecommend[0].tupian.split(',')[0]:''" alt="" />
																																																																																						    			<div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}' v-if="true" class="list-4-item-center">
																					<div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"50%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}' class="list-4-item-title">{{shangpinxinxiRecommend[0].shangpinmingcheng}}</div>
																																																																																																																																    			</div>
		    </div>
		    <div class="list-4-body">
		    			<div v-if="shangpinxinxiRecommend.length>1" @click="jump('../shangpinxinxi/detail.html?id='+shangpinxinxiRecommend[1].id)" class="list-4-item animation-box item-2" :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0, .3)","margin":"0 20px 20px 0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"220px","borderStyle":"solid","height":"190px"}'>
		    			  		    			  		    			  		    			  		    			  		    			  		    			  <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}' :src="shangpinxinxiRecommend[1].tupian?shangpinxinxiRecommend[1].tupian.split(',')[0]:''" alt="" />
		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}' v-if="true" class="list-4-item-center">
		    			  			    			  			    			  	<div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"50%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}' class="list-4-item-title">{{shangpinxinxiRecommend[1].shangpinmingcheng}}</div>
		    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  </div>
		    			</div>
		    			<div v-if="shangpinxinxiRecommend.length>2" @click="jump('../shangpinxinxi/detail.html?id='+shangpinxinxiRecommend[2].id)" class="list-4-item animation-box item-3" :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0, .3)","margin":"0 20px 0 0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"220px","borderStyle":"solid","height":"190px"}'>
		    			  		    			  		    			  		    			  		    			  		    			  		    			  <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}' :src="shangpinxinxiRecommend[2].tupian?shangpinxinxiRecommend[2].tupian.split(',')[0]:''" alt="" />
		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}' v-if="true" class="list-4-item-center">
		    			  			    			  			    			  	<div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"50%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}' class="list-4-item-title">{{shangpinxinxiRecommend[2].shangpinmingcheng}}</div>
		    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  </div>
		    			</div>
		    </div>
		    <div class="list-4-body">
		    			<div v-if="shangpinxinxiRecommend.length>3" @click="jump('../shangpinxinxi/detail.html?id='+shangpinxinxiRecommend[3].id)" class="list-4-item animation-box item-4" :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0, .3)","margin":"0 0 20px 0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"190px","borderStyle":"solid","height":"190px"}'>
		    			  		    			  		    			  		    			  		    			  		    			  		    			  <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}' :src="shangpinxinxiRecommend[3].tupian?shangpinxinxiRecommend[3].tupian.split(',')[0]:''" alt="" />
		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}' v-if="true" class="list-4-item-center">
		    			  			    			  			    			  	<div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"50%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}' class="list-4-item-title">{{shangpinxinxiRecommend[3].shangpinmingcheng}}</div>
		    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  </div>
		    			</div>
		    			<div v-if="shangpinxinxiRecommend.length>4" @click="jump('../shangpinxinxi/detail.html?id='+shangpinxinxiRecommend[4].id)" class="list-4-item animation-box item-5" :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0, .3)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"190px","borderStyle":"solid","height":"190px"}'>
		    			  		    			  		    			  		    			  		    			  		    			  		    			  <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}' :src="shangpinxinxiRecommend[4].tupian?shangpinxinxiRecommend[4].tupian.split(',')[0]:''" alt="" />
		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  		    			  <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}' v-if="true" class="list-4-item-center">
		    			  			    			  			    			  	<div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"50%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}' class="list-4-item-title">{{shangpinxinxiRecommend[4].shangpinmingcheng}}</div>
		    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  			    			  </div>
		    			</div>
		    </div>
		</div>
		<div class="clear"></div>
		<div style="text-align: center;">
		<button @click="jump('../shangpinxinxi/list.html')" style="display: block;cursor: pointer;" type="button" :style='{"padding":"0 15px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"4px auto","borderColor":"#ccc","backgroundColor":"rgba(227, 249, 253, 0)","color":"rgba(14, 14, 14, 1)","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"16px","borderStyle":"solid","height":"34px"}'>查看更多<i v-if="false" :style='{"isshow":false,"padding":"0px 0 0 3px","fontSize":"16px","color":"rgba(33, 31, 31, 1)"}' class="layui-icon layui-icon-next"></i></button>
		</div>
	  </div>
	</div>

	<!-- 商品资讯 -->
	<!-- 样式二 -->
	<div class="news index-pv2" style="display: flex;justify-content: center;width:100%" :style='{"padding":"0px 0 0px 0","boxShadow":" 0px ","margin":"0px ","borderColor":"rgba(255, 255, 255, 0)","backgroundColor":"rgba(6, 82, 121, 0.4)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
	  <div class="box" style='width:88%'>
	    <div class="title" :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px ","margin":"0px 0 10px 0","borderColor":"rgba(123, 104, 238, 0)","backgroundColor":"rgba(6, 82, 121, 0.4)","color":"rgba(23, 23, 24, 1)","borderRadius":"0px","alignItems":"center","borderWidth":"0px 30px 0px  0px","fontSize":"20px","borderStyle":"solid"}'>
	      <span>HOME NEWS</span><span>商品资讯</span>
	    </div>
	    <div class="list">
	      <div v-for="(item,index) in newsList" :key="index" class="list-item" @click="jump('../news/detail.html?id='+item.id)" :class="2==='1'?'list-item1':2==='3'?'list-item3':''" &gt;>
	        <div class="list-item-body animation-box" :style='{"padding":"6px","boxShadow":"0 0 0px","margin":"0 0 10px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
			  <img :style='{"boxShadow":"0px 0px 0px #88ada6","borderColor":"rgba(0,0,0,.3)","borderRadius":"4px","borderWidth":"0","width":"280px","borderStyle":"solid","height":"230px"}' :src="item.picture" alt="" />
	          <div class="item-info">
				<div v-if='true' :style='{"isshow":true,"padding":"0","margin":"5px 0","backgroundColor":"rgba(0,0,0,0)","color":"rgba(16, 15, 15, 1)","borderRadius":"0","textAlign":"left","fontSize":"20px"}' class="name">{{item.title}}</div>
				<div :style='{"padding":"0","margin":"0","backgroundColor":"rgba(0,0,0,0)","color":"rgba(96, 90, 90, 1)","borderRadius":"0","textAlign":"left","fontSize":"12px"}' class="time">{{item.addtime}}</div>
	          </div>
	        </div>
	      </div>
	    </div>
	    <div class="clear"></div>
	    <div style="text-align: center;">
		<el-button @click="jump('../news/list.html')" style="display: block;cursor: pointer;" type="button" :style='{"padding":"0 15px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"4px auto","borderColor":"#ccc","backgroundColor":"rgba(227, 249, 253, 0)","color":"rgba(14, 14, 14, 1)","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"16px","borderStyle":"solid","height":"34px"}'>查看更多<i v-if="false" :style='{"isshow":false,"padding":"0px 0 0 3px","fontSize":"16px","color":"rgba(33, 31, 31, 1)"}' class="layui-icon layui-icon-next"></i></el-button>
	    </div>
	  </div>
	</div>

	<!-- 特价商品展示 -->

    </div>
  </div>

  <script src="../../layui/layui.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/Swiper/5.4.5/js/swiper.min.js"></script>
  <script src="../../js/vue.js"></script>
  <script src="../../js/env-config.js"></script>
  <script src="../../js/config.js"></script>
  <script src="../../modules/config.js"></script>
  <script src="../../js/utils.js"></script>
  <script type="text/javascript">
    var vue = new Vue({
      el: '#app',
      data: {
            queryList:[
                {
                    queryName:"商品名称",
                },
            ],
            queryIndex: 0,

        shangpinxinxiRecommend: [],

        swiperList: [],
        // 商品资讯
        newsList: [],
        leftNewsList: [],
        rightNewsList: []
      },
      filters: {
        newsDesc: function(val) {
          if (val) {
            val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
            if (val.length > 60) {
              val = val.substring(0, 60);
            }

            return val;
          }
          return '';
        }
      },
      methods: {
        jump(url) {
		if (this.queryIndex == 0) {
			localStorage.setItem('indexQueryCondition', document.getElementById("shangpinxinxishangpinmingcheng").value);
		}
          	jump(url)
        },
	queryChange(event) {
		this.queryIndex = event.target.value;
		if (this.queryIndex == 0) {
			this.shangpinxinxishangpinmingcheng = this.queryList[event.target.value].queryName;
		}
	}
      }
    });

    layui.use(['layer', 'form', 'element', 'carousel', 'http', 'jquery'], function() {
		var layer = layui.layer;
		var element = layui.element;
		var form = layui.form;
		var carousel = layui.carousel;
		var http = layui.http;
		var jquery = layui.jquery;

      // 获取轮播图 数据
      http.request('config/list', 'get', {
        page: 1,
        limit: 5
      }, function(res) {
        if (res.data.list.length > 0) {
          let swiperList = [];
          res.data.list.forEach(element => {
            if (element.value != null) {
              swiperList.push({
                img: element.value
              });
            }
          });

		  vue.swiperList = swiperList;
		  // 渲染轮播图
		  vue.$nextTick(() => {
		    carousel.render({
		    	elem: '#test1',
				width: '100%',
		    	height: '600px',
		    	arrow: 'hover',
		    	anim: 'default',
		    	autoplay: 'true',
		    	interval: '3000',
		    	indicator: 'inside'
		    });

		  })

          // vue.$nextTick(()=>{
          //   window.xznSlide();
          // });
        }
      });

      // 商品资讯
      http.request('news/list', 'get', {
        page: 1,
	limit: 2 * 2,
        order: 'desc'
      }, function(res) {
        var newsList = res.data.list;
		for(var i = 0; i<newsList.length; i++) {
			let d = newsList[i].addtime.split(' ')
			d = d[0].split('-')
			newsList[i].year = d[0]
			newsList[i].month = d[1] + '-' + d[2]
		}
		
        vue.newsList = newsList;
        if (newsList.length > 0 && newsList.length <= 2) {
          vue.leftNewsList = res.data.list
        } else {
          var leftNewsList = []
          for (let i = 0; i <= 2; i++) {
            leftNewsList.push(newsList[i]);
          }
          vue.leftNewsList = leftNewsList
        }
        if (newsList.length > 2 && newsList.length <= 8) {
          var rightNewsList = []
          for (let i = 3; i <= newsList.length; i++) {
            rightNewsList.push(newsList[i]);
          }
          vue.rightNewsList = rightNewsList
        }

		let flag = 2;
		let options = {"navigation":{"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},"slidesPerView":5,"loop":true,"spaceBetween":20,"autoplay":{"delay":3000,"disableOnInteraction":false}}
		options.pagination = {el:'null'}
		if(flag == 3) {
			vue.$nextTick(() => {
                                                                                                                                                                				new Swiper ('#newsnews', options)
                                			})
		}
		
		if(flag == 6) {
			let sixSwiper = {
				loop: true,
				speed: $template2.front.index.news.six.speed,
				slidesPerView: 3,
				spaceBetween: $template2.front.index.news.six.spaceBetween,
				centeredSlides: true,
				watchSlidesProgress: true,
				autoplay: {
				  delay: 0,
				  stopOnLastSlide: false,
				  disableOnInteraction: false
				}
			}
			
			vue.$nextTick(() => {
				new Swiper('#new-list-6', sixSwiper)
			})
		}
      });

      // 获取推荐信息
      http.request(`shangpinxinxi/autoSort`, 'get', {
        page: 1,
		}, function(res) {
			vue.shangpinxinxiRecommend = res.data.list
			let flag = 4;
			let options = {"navigation":{"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},"slidesPerView":5,"loop":true,"spaceBetween":20,"autoplay":{"delay":3000,"disableOnInteraction":false}}
			options.pagination = {el:'null'}
			if(flag == 3) {
				vue.$nextTick(() => {
																																				new Swiper ('#recommendshangpinxinxi', options)
																																																																																														})
			}
			
			// 商品推荐样式五
			if(flag == 5) {
				vue.$nextTick(() => {
					var swiper = new Swiper('#recommend-five-swiper', {
						loop: true,
						speed: 500,
						slidesPerView: 5,
						spaceBetween: 10,
						autoplay: {"delay":3000,"disableOnInteraction":false},
						centeredSlides: true,
						watchSlidesProgress: true,
						on: {
							setTranslate: function() {
								slides = this.slides
								for (i = 0; i < slides.length; i++) {
									slide = slides.eq(i)
									progress = slides[i].progress
									// slide.html(progress.toFixed(2)); //看清楚progress是怎么变化的
									slide.css({
										'opacity': '',
										'background': ''
									});
									slide.transform(''); //清除样式
					
									slide.transform('scale(' + (1.5 - Math.abs(progress) / 4) + ')');
								}
							},
							setTransition: function(transition) {
								for (var i = 0; i < this.slides.length; i++) {
									var slide = this.slides.eq(i)
									slide.transition(transition);
								}
							},
						},
						navigation: {"nextEl":".swiper-button-next","prevEl":".swiper-button-prev"},
						pagination: {"el":".swiper-pagination","clickable":true},
					});
				})
			}
		});

  });

  window.xznSlide = function() {
    // jQuery(".banner").slide({mainCell:".bd ul",autoPlay:true,interTime:5000});
    jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_piclist ul",effect:"leftLoop", delayTime:200, autoPlay:true,triggerTime:0});
    jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_tx ul",delayTime:0, autoPlay:true});
    jQuery(".product_list").slide({mainCell:".bd ul",autoPage:true,effect:"leftLoop",autoPlay:true,vis:5,trigger:"click",interTime:4000});
  };
</script>
<script src="../../xznstatic/js/index.js"></script>
</body>
</html>

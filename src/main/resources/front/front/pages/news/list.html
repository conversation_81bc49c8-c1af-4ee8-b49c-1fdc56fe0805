
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="utf-8">
<title>商品资讯</title>
<meta name="keywords" content="" />
<meta name="description" content="" />
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<link rel="stylesheet" href="../../layui/css/layui.css">
<link rel="stylesheet" href="../../xznstatic/css/common.css"/>
<link rel="stylesheet" href="../../xznstatic/css/style.css"/>
<script type="text/javascript" src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
</head>
<style>
	#test1 {
		overflow: hidden;
	}
	#test1 .layui-carousel-ind li {
		width: 16px;
		height: 4px;
		border-width: 0;
		border-style: solid;
		border-color: rgba(0,0,0,.3);
		border-radius: 0px;
		background-color: rgba(255, 255, 255, 1);
		box-shadow: 0 0 0px rgba(255,0,0,.8);
	}
	#test1 .layui-carousel-ind li.layui-this {
		width: 16px;
		height: 4px;
		border-width: 0;
		border-style: solid;
		border-color: rgba(0,0,0,.3);
		border-radius: 0px;
		background-color: rgba(0, 85, 119, 1);
		box-shadow: 0 0 0px rgba(0, 85, 119, 1);
	}
	
	// 列表
		.recommend {
			padding: 10px 0;
			display: flex;
			justify-content: center;
			background-repeat: no-repeat;
			background-position: center center;
			background-size: cover;
		}
		.recommend .box {
		    width: 1002px;
			margin: 0 auto;
		}
		.recommend .box .title {
			padding: 10px 5px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			box-sizing: border-box;
		}
		.recommend .box .title span {
			padding: 0 10px;
			font-size: 16px;
			line-height: 1.4;
		}
	
		.recommend .box .filter {
			padding: 0 10px;
			display: flex;
			align-items: center;
			box-sizing: border-box;
			width: 100%;
			flex-wrap: wrap;
		}
		.recommend .box .filter .item-list {
			display: flex;
			align-items: center;
		}
		.recommend .box .filter .item-list .lable {
			font-size: 14px;
			color: #333;
			box-sizing: border-box;
		}
	
		.recommend .box .list {
			display: flex;
			flex-wrap: wrap;
		}
		  	  	  		.recommend .box .list .list-item {
			flex: 0 0 20%;
			padding: 0 5px;
			box-sizing: border-box;
		}
		.recommend .box .list .list-item .list-item-body {
			cursor: pointer;
			border: 1px solid rgba(0, 0, 0, 3);
			padding: 5px;
			box-sizing: border-box;
		}
		.recommend .box .list .list-item-body img {
			width: 100%;
			height: 100px;
			display: block;
			margin: 0 auto;
	    object-fit: cover;
	    max-width: 100%;
		}
	
		.recommend .box .list .list-item-body .info {
			display: flex;
			flex-wrap: wrap;
		}
		.recommend .box .list .list-item-body .info .price {
			padding-top: 5px;
			color: red;
			font-size: 14px;
			text-align: center;
			box-sizing: border-box;
		}
	
		.recommend .box .list .list-item-body .info .name {
			padding-top: 5px;
			color: red;
			font-size: 14px;
			text-align: center;
			box-sizing: border-box;
		}
	
		.recommend .box .list .list-item3 {
			flex: 0 0 33.33%;
		}
	
		.recommend .box .list .list-item5 {
			flex: 0 0 20%;
		}
	
		.recommend .box .news {
			display: flex;
			flex-wrap: wrap;
    width: 100%;
		}
										.recommend .box .news .list-item {
			flex: 0 0 50%;
			padding: 0 10px;
			box-sizing: border-box;
		}
		.recommend .box .news .list-item .list-item-body {
			cursor: pointer;
			border: 1px solid rgba(0, 0, 0, 3);
			padding: 10px;
			box-sizing: border-box;
			display: flex;
		}
	
		.recommend .box .news .list-item .list-item-body img {
			width: 120px;
			height: 100%;
			display: block;
			margin: 0 auto;
	    object-fit: cover;
	    max-width: 100%;
		}
	
		.recommend .box .news .list-item .list-item-body .item-info {
			flex: 1;
			display: block;
			justify-content: space-between;
			flex-direction: column;
			padding-left: 10px;
			box-sizing: border-box;
		}
	
		.recommend .box .news .list-item .list-item-body .item-info .name {
			padding-top: 5px;
			color: red;
			font-size: 14px;
			box-sizing: border-box;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
		}
	
		.recommend .box .news .list-item .list-item-body .item-info .time {
			padding-top: 5px;
			color: red;
			font-size: 14px;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 5;
			-webkit-box-orient: vertical;
			box-sizing: border-box;
		}
	
		.recommend .box .news .list-item1 {
			flex: 0 0 100%;
		}
	
		.recommend .box .news .list-item3 {
			flex: 0 0 33.33%;
		}
	
	  .index-pv2 .animation-box:hover {
	  	transform: perspective(1000px) translate3d(0px, 0px, 0px) scale(1.2) rotate(0deg) skew(-2deg, 0deg);
	  	transition: all 0.6s;
	  }
	  
	  .layui-laypage .layui-laypage-count {
	    padding: 0 10px;
	  }
	  .layui-laypage .layui-laypage-skip {
	    padding-left: 10px;
	  }
</style>
<body>
  <div id="app">
    <div class="banner">
		<div class="layui-carousel" id="test1" :style='{"boxShadow":"0 0px 0px #fff143","margin":"0 ","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
		  <div carousel-item>
			<div v-for="(item,index) in swiperList" :key="index">
				<img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" />
			</div>
		  </div>
		</div>
      <!-- <div class="bd">
        <ul>
          <li v-for="(item,index) in swiperList" v-bind:key="index"><img :src="item.img" /></li>
        </ul>
        <div class="hd">
          <ul>
            <li v-for="(item,index) in swiperList" v-bind:key="index"></li>
          </ul>
        </div>
      </div> -->
    </div>

    <!-- <div class="news_con con_1000">
      <div class="ny_right" style="width: 100%;">
        <div class="title">
          <p>您现在的位置 : 商品资讯</p>
          <h3> 商品资讯</h3>
        </div>
        <div class="content">
          <div class="first_list" v-for="(item,index) in dataList" v-bind:key="index" @click="jump('../news/detail.html?id='+item.id);" v-if="item.picture.substr(0,7)=='http://'"> 
            <img :src="item.picture">
            <div class="right" style="width: 750px;"> 
              <a href="javascript:void(0);" class="tit">{{item.title}}</a>
              <div class="text">{{item.content|newsDesc}}...</div>
              <a href="javascript:void(0);">[查看详情]</a> <span class="date">{{item.addtime}}</span>
            </div>
          </div>
          <ul class="other_list">
            <li v-for="(item,index) in dataList" v-bind:key="index" v-if="item.picture.substr(0,7)!='http://'">
              <a href="#" @click="jump('../news/detail.html?id='+item.id)"> {{item.title}}</a><span>[{{item.addtime}}]</span>
            </li>
          </ul>
          <div class="pager" id="pager" :style="{textAlign:1==1?'left':1==2?'center':'right'}"></div>
        </div>
      </div>
    </div> -->
	
	<div class="recommend index-pv2" :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px ","margin":"0px 0 0 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 255, 255, 1)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
	  <div class="box" style='width:80%'>
	    <div class="title" :style='{"padding":"10px 0 10px 0","boxShadow":"6px 0 6px #ffb3a7","margin":"10px 0 10px 0","borderColor":"rgba(255, 179, 167, 1)","backgroundColor":"#f7f7f7","borderRadius":"2px","borderWidth":"0px","borderStyle":"solid","justifyContent":"space-between","height":"70px"}'>
	      <span :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(255,0,0,1)","backgroundColor":"rgba(0,0,0,0)","color":"rgba(74, 66, 102, 1)","borderRadius":"0 0 2px 0","borderWidth":"0","fontSize":"22px","borderStyle":"solid"}'>商品资讯</span><span :style='{"padding":"0 10px","boxShadow":"0 0 6px rgba(255,0,0,0)","borderColor":"rgba(0,0,0,0)","backgroundColor":"rgba(0,0,0,0)","color":"rgba(22, 133, 169, 1)","borderRadius":"0","borderWidth":"0","fontSize":"16px","borderStyle":"solid"}'>您现在的位置：商品资讯</span>
	    </div>
	    <!-- 样式二 -->
	    <div class="list news">
	      <div @click="jump('../news/detail.html?id='+item.id);" v-for="(item,index) in dataList" :key="index" class="list-item" :class="2=='1'?'list-item1':2=='3'?'list-item3':''">
	        <div class="list-item-body animation-box" :style='{"padding":"10px","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0 0 10px 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
	          <img :style='{"boxShadow":"8px 0 6px #a1afc9","borderColor":"rgba(0,0,0,.3)","borderRadius":"0","borderWidth":"0","width":"170px","borderStyle":"solid","height":"200px"}' :src="item.picture">
	          <div class="item-info">
				  <div v-if="true" :style='{"isshow":true,"padding":"0","margin":"5px 0 0 0","backgroundColor":"rgba(0,0,0,0)","color":"rgba(66, 80, 102, 1)","borderRadius":"0","textAlign":"left","width":"100%","fontSize":"18px"}' class="name">{{item.title}}</div>
				  <div v-if="item.introduction" :style='{"padding":"0","margin":"10px 0 0 0","backgroundColor":"rgba(0,0,0,0)","color":"rgba(6, 82, 121, 1)","borderRadius":"0","textAlign":"left","width":"100%","fontSize":"13px"}' class="time">{{item.introduction}}</div>
	          </div>
	        </div>
	      </div>
	    </div>
		<div class="pager" id="pager"  :style="{textAlign:1==1?'left':1==2?'center':'right'}"></div>
	  </div>
	</div>
	
  </div>

  <script src="../../layui/layui.js"></script>
  <script src="../../js/vue.js"></script>
  <script src="../../js/config.js"></script>
  <script src="../../modules/config.js"></script>
  <script src="../../js/utils.js"></script>

  <script type="text/javascript">
    var vue = new Vue({
      el: '#app',
      data: {
        swiperList: [],
        dataList: []
      },
      filters: {
        newsDesc: function(val) {
          if (val) {
            if (val.length > 60) {
              return val.substring(0, 60).replace(/<[^>]*>/g).replace(/undefined/g, '');
            } else {
              return val.replace(/<[^>]*>/g).replace(/undefined/g, '');
            }
          }
          return '';
        }
      },
      methods: {
        jump(url) {
          jump(url)
        }
      }
    });

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery'], function() {
      var layer = layui.layer;
			var element = layui.element;
			var carousel = layui.carousel;
			var laypage = layui.laypage;
			var http = layui.http;
			var jquery = layui.jquery;

      var limit = 10;

      // 获取轮播图 数据
      http.request('config/list', 'get', {
        page: 1,
        limit: 5
      }, function(res) {
        if (res.data.list.length > 0) {
			let swiperList = [];
			res.data.list.forEach(element => {
				if (element.value != null) {
				  swiperList.push({
					img: element.value
				  });
				}
			});
			vue.swiperList = swiperList;
          
			vue.$nextTick(() => {
				carousel.render({
					elem: '#test1',
					width: '100%',
					height: '600px',
					arrow: 'hover',
					anim: 'default',
					autoplay: 'true',
					interval: '3000',
					indicator: 'inside'
				});
			})
		}
      });

      // 获取列表数据
      http.request('news/list', 'get', {
        page: 1,
        limit: limit
      }, function(res) {
        vue.dataList = res.data.list
        // 分页
        laypage.render({
          elem: 'pager',
          count: res.data.total,
          limit: limit,
		  groups: 3,
		  layout: ["prev","page","next"],
		  theme: '#065279',
          jump: function(obj, first) {
            //首次不执行
            if (!first) {
              http.request('news/list', 'get', {
                page: obj.curr,
                limit: obj.limit
              }, function(res) {
                vue.dataList = res.data.list
              })
            }
          }
        });
      });                             
    });

    window.xznSlide = function() {
      jQuery(".banner").slide({mainCell:".bd ul",autoPlay:true,interTime:5000});
      jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_piclist ul",effect:"leftLoop", delayTime:200, autoPlay:true,triggerTime:0});
      jQuery("#ifocus").slide({ titCell:"#ifocus_btn li", mainCell:"#ifocus_tx ul",delayTime:0, autoPlay:true});
      jQuery(".product_list").slide({mainCell:".bd ul",autoPage:true,effect:"leftLoop",autoPlay:true,vis:5,trigger:"click",interTime:4000});
    };
  </script>
</body>
</html>

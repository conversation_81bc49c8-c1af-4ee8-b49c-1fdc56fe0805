
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>注册</title>
		<!-- bootstrap样式，地图插件使用 -->
		<link rel="stylesheet" href="../../css/bootstrap.min.css" />
		<link rel="stylesheet" href="../../layui/css/layui.css">
		<!-- 样式 -->
		<link rel="stylesheet" href="../../css/style.css" />
		<!-- 主题（主要颜色设置） -->
		<link rel="stylesheet" href="../../css/theme.css" />
		<!-- 通用的css -->
		<link rel="stylesheet" href="../../css/common.css" />
	</head>
	<style>
		html::after {
			position: fixed;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			content: '';
			display: block;
			background-attachment: fixed;
			background-size: cover;
			background-position: center;
						z-index: -1;
		}
		#swiper {
			overflow: hidden;
			margin: 0 auto;
		}
		#swiper .layui-carousel-ind li {
			width: 16px;
			height: 4px;
			border-width: 0;
			border-style: solid;
			border-color: rgba(0,0,0,.3);
			border-radius: 0px;
			background-color: rgba(255, 255, 255, 1);
			box-shadow: 0 0 0px rgba(255,0,0,.8);
		}
		#swiper .layui-carousel-ind li.layui-this {
			width: 16px;
			height: 4px;
			border-width: 0;
			border-style: solid;
			border-color: rgba(0,0,0,.3);
			border-radius: 0px;
			background-color: rgba(0, 85, 119, 1);
			box-shadow: 0 0 0px rgba(0, 85, 119, 1);
		}
		
		button, button:focus {
			outline: none;
		}
		
		.data-add-container .add .layui-select-title .layui-unselect {
			padding: 0 12px;
			height: 50px;
			font-size: 16px;
			color: #333;
			border-radius: 4px;
			border-width: 1px;
			border-style: solid;
			border-color: rgba(107, 104, 130, 1);
			background-color: #fff;
			box-shadow: 0 0 0px rgba(255,0,0,.8);
			text-align: left;
		}
		.data-add-container .add .layui-form-item {
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.data-add-container .layui-form-pane .layui-form-item[pane] .layui-form-label {
			margin: 0;
			position: inherit;
			background: transparent;
			border: 0;
		}
		.data-add-container .add .layui-input-block {
			margin: 0;
			flex: 1;
		}
		.data-add-container .layui-form-pane .layui-form-item[pane] .layui-input-inline {
			margin: 0;
			flex: 1;
			display: flex;
		}
	</style>
	<body style="background: #EEEEEE; ">


		<div id="app">

			<!--
			<div class="layui-carousel" id="swiper">
				<div carousel-item id="swiper-item">
					<div v-for="(item,index) in swiperList" v-bind:key="index">
						<img class="swiper-item" :src="item.img">
					</div>
				</div>
			</div> -->
			<div class="layui-carousel" id="swiper" :style='{"boxShadow":"0 0px 0px #fff143","margin":"0 ","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
			  <div carousel-item id="swiper-item">
				<div v-for="(item,index) in swiperList" :key="index">
					<img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" />
				</div>
			  </div>
			</div>
			<!-- 轮播图 -->

			<div class="data-add-container" :style='{"padding":"20px","boxShadow":"0px 0px 0px rgba(255,0,0,.8)","margin":"50px auto","borderColor":"rgba(255, 0, 0, 0)","backgroundColor":"#fff","borderRadius":"10px","borderWidth":"0","borderStyle":"solid"}'>

				<form class="layui-form layui-form-pane add" lay-filter="myForm">
					
                                                            <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">用户名：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.username" type="text" :readonly="ro.username" name="username" id="username" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">密码：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.password" type="text" :readonly="ro.password" name="password" id="password" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                                                <div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item" pane>
						<label  :style='{"width":"110px","padding":"0 12px 0 0","fontSize":"16px","color":"rgba(107, 104, 130, 1)","textAlign":"left"}' class="layui-form-label">角色：</label>
						<div class="layui-input-block">
							<input :style='{"padding":"0 12px","boxShadow":"0 0 0px rgba(255,0,0,.8)","borderColor":"rgba(107, 104, 130, 1)","backgroundColor":"#fff","color":"#333","borderRadius":"4px","textAlign":"left","borderWidth":"1px","fontSize":"16px","borderStyle":"solid","height":"50px"}' v-model="detail.role" type="text" :readonly="ro.role" name="role" id="role" autocomplete="off" class="layui-input">
						</div>
					</div>
                                                            
                                                                                                                                            
					<div  :style='{"padding":"10px","boxShadow":"0 0 0px #1685a9","margin":"0 0 10px 0","borderColor":"rgba(22, 133, 169, 1)","backgroundColor":"#fff","borderRadius":"0px","borderWidth":"0 0 1px 0","borderStyle":"solid"}' class="layui-form-item">
						<div class="layui-input-block" style="text-align: right;">
							<button :style='{"padding":"0 10px","boxShadow":"8px 6px 6px #a1afc9","margin":"0 10px","borderColor":"#ccc","backgroundColor":"rgba(23, 124, 176, 1)","color":"#fff","borderRadius":"6px","borderWidth":"0","width":"25%","fontSize":"16px","borderStyle":"solid","height":"50px"}' class="layui-btn btn-submit" lay-submit lay-filter="*">提交</button>
							<button :style='{"padding":"0 10px","boxShadow":"6px 5px 6px #c2ccd0","margin":"0 10px","borderColor":"#ccc","backgroundColor":"rgba(214, 236, 240, 1)","color":"rgba(219, 90, 107, 1)","borderRadius":"6px","borderWidth":"0","width":"25%","fontSize":"16px","borderStyle":"solid","height":"50px"}' type="reset" class="layui-btn layui-btn-primary">重置</button>
						</div>
					</div>
				</form>
			</div>
		</div>

		<script src="../../layui/layui.js"></script>
		<script src="../../js/vue.js"></script>
		<!-- 组件配置信息 -->
		<script src="../../js/config.js"></script>
		<!-- 扩展插件配置信息 -->
		<script src="../../modules/config.js"></script>
		<!-- 工具方法 -->
		<script src="../../js/utils.js"></script>
		<!-- 校验格式工具类 -->
		<script src="../../js/validate.js"></script>
		<!-- 地图 -->
		<script type="text/javascript" src="../../js/jquery.js"></script>
		<script type="text/javascript" src="http://webapi.amap.com/maps?v=1.3&key=ca04cee7ac952691aa67a131e6f0cee0"></script>
		<script type="text/javascript" src="../../js/bootstrap.min.js"></script>
		<script type="text/javascript" src="../../js/bootstrap.AMapPositionPicker.js"></script>

		<script>
			var jquery = $;
			
			var vue = new Vue({
				el: '#app',
				data: {
					// 轮播图
					swiperList: [{
						img: '../../img/banner.jpg'
					}],
					dataList: [],
      					ro:{
				        username : false,
				        password : false,
				        role : false,
              				},
                    detail: {
                                                                        username: '',
                                                                                                password: '',
                                                                                                role: '',
                                                                    },
                    																																			centerMenu: centerMenu
				},
				updated: function() {
					layui.form.render('select', 'myForm');
				},
                computed: {
                                                                                                                                                                                                                        },
				methods: {
					jump(url) {
						jump(url)
					}
				}
			})

			
			layui.use(['layer', 'element', 'carousel', 'http', 'jquery', 'form', 'upload', 'laydate','tinymce'], function() {
				var layer = layui.layer;
				var element = layui.element;
				var carousel = layui.carousel;
				var http = layui.http;
				var jquery = layui.jquery;
				var form = layui.form;
				var upload = layui.upload;
				var laydate = layui.laydate;
                var tinymce = layui.tinymce

				// 获取轮播图 数据
				http.request('config/list', 'get', {
					page: 1,
					limit: 5
				}, function(res) {
					if (res.data.list.length > 0) {
						var swiperItemHtml = '';
						for (let item of res.data.list) {
							if (item.name.indexOf('picture') >= 0 && item.value && item.value != "" && item.value != null) {
								swiperItemHtml +=
									'<div>' +
									'<img style="width: 100%;height: 100%;object-fit:cover;" class="swiper-item" src="' + item.value + '">' +
									'</div>';
							}
						}
						jquery('#swiper-item').html(swiperItemHtml);
						// 轮播图
						vue.$nextTick(() => {
						  carousel.render({
						  	elem: '#swiper',
							width: '100%',
						  	height: '600px',
						  	arrow: 'hover',
						  	anim: 'default',
						  	autoplay: 'true',
						  	interval: '3000',
						  	indicator: 'inside'
						  });
						
						})
						// carousel.render({
						// 	elem: '#swiper',
						// 	width: swiper.width,height:swiper.height,
						// 	arrow: swiper.arrow,
						// 	anim: swiper.anim,
						// 	interval: swiper.interval,
						// 	indicator: "none"
						// });
					}
				});

                																												

                // 跨表
                if(http.getParam('corss')){
					var obj = JSON.parse(localStorage.getItem('crossObj'));
					for (var o in obj){
					if(o=='username'){
          	                                vue.detail[o] = obj[o];
						vue.ro.username = true;
						continue;
                                        }
					if(o=='password'){
          	                                vue.detail[o] = obj[o];
						vue.ro.password = true;
						continue;
                                        }
					if(o=='role'){
          	                                vue.detail[o] = obj[o];
						vue.ro.role = true;
						continue;
                                        }
                                        }
				}
				

				// 提交
				form.on('submit(*)', function(data) {
					data = data.field;
					
                    // 数据校验
                    if(!data.username){
                        layer.msg('用户名不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                                    if(!data.password){
                        layer.msg('密码不能为空', {
							time: 2000,
							icon: 5
						});
                        return false
                    }
                                                                                                                                                                                                                                                                                                            
					// 跨表计算
					                                                                                                                        					
                    // 比较大小
                                                                                                                                                                                                                                                                                                                                                                        					
					// 提交数据
					http.requestJson('users' + '/add', 'post', data, function(res) {
					 	layer.msg('提交成功', {
					 		time: 2000,
					 		icon: 6
					 	}, function() {
					 		back();
						});
					 });

					return false
				});

			});
		</script>
	</body>
</html>

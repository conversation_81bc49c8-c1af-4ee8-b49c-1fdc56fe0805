<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ShangpinxinxiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ShangpinxinxiEntity" id="shangpinxinxiMap">
        <result property="shangpinmingcheng" column="shangpinmingcheng"/>
        <result property="shangpinfenlei" column="shangpinfenlei"/>
        <result property="tupian" column="tupian"/>
        <result property="pinpai" column="pinpai"/>
        <result property="guige" column="guige"/>
        <result property="shangpinxiangqing" column="shangpinxiangqing"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="price" column="price"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ShangpinxinxiVO" >
		SELECT * FROM shangpinxinxi  shangpinxinxi         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ShangpinxinxiVO" >
		SELECT  shangpinxinxi.* FROM shangpinxinxi  shangpinxinxi 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ShangpinxinxiView" >

		SELECT  shangpinxinxi.* FROM shangpinxinxi  shangpinxinxi 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ShangpinxinxiView" >
		SELECT * FROM shangpinxinxi  shangpinxinxi <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
</mapper>
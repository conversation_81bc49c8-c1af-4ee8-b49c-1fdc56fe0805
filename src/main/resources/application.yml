# Tomcat
server:
    tomcat:
        uri-encoding: UTF-8
    port: 8080
    servlet:
        context-path: /shoppingonline

spring:
    datasource:
        driverClassName: com.mysql.cj.jdbc.Driver
        url: ************************************************************************************************************************************************************************
        username: root
        password: 147678

#        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#        url: ***********************************************************
#        username: sa
#        password: 123456

    servlet:
      multipart:
        max-file-size: 10MB
        max-request-size: 10MB
    resources:
      static-locations: classpath:static/,file:static/

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.entity
  global-config:
    db-config:
      #主键类型 AUTO:"数据库ID自增", INPUT:"用户输入ID", ASSIGN_ID:"分配ID", ASSIGN_UUID:"分配UUID"
      id-type: AUTO
      #字段策略 IGNORED:"忽略判断", NOT_NULL:"非NULL判断", NOT_EMPTY:"非空判断", DEFAULT:"默认", NEVER:"不加入SQL"
      field-strategy: NOT_EMPTY
      #逻辑删除配置
      logic-delete-value: -1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    #springboot 项目mybatis plus 设置 jdbcTypeForNull (oracle数据库需配置JdbcType.NULL, 默认是Other)
    jdbc-type-for-null: 'null'

# springdoc-openapi配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

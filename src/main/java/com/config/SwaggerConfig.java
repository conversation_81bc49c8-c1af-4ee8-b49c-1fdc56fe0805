package com.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("网上商城购物系统 API")
                        .version("2.0")
                        .description("Spring Boot 网上商城购物系统的API文档")
                        .contact(new Contact()
                                .name("开发团队")
                                .email("<EMAIL>")));
    }
}
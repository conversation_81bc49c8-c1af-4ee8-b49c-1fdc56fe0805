# 修复管理后台登录问题

## 问题描述
管理后台登录时出现404错误，请求仍然使用旧的路径 `springboot7z60r` 而不是新的路径 `shoppingonline`。

## 错误信息
```
POST http://localhost:8080/springboot7z60r/yonghu/login?username=用户1&password=123456 404 (Not Found)
```

## 原因分析
虽然我们已经修改了环境配置文件 `.env`，但Vue应用的构建版本仍然使用的是旧的环境变量。Vue应用需要重新构建才能使用新的配置。

## 解决方案

### 方案1：重新构建Vue应用（推荐）

#### Windows用户：
1. 双击运行 `rebuild-admin.bat` 脚本
2. 或者手动执行以下命令：
   ```cmd
   cd src\main\resources\admin\admin
   npm install
   npm run build
   ```

#### Linux/Mac用户：
1. 运行脚本：
   ```bash
   chmod +x rebuild-admin.sh
   ./rebuild-admin.sh
   ```
2. 或者手动执行：
   ```bash
   cd src/main/resources/admin/admin
   npm install
   npm run build
   ```

### 方案2：使用开发服务器（开发调试用）

如果您正在开发调试，可以使用Vue开发服务器：

```bash
cd src/main/resources/admin/admin
npm run serve
```

然后访问：`http://localhost:8081`

### 方案3：验证修复结果

重新构建后，访问管理后台：
```
http://localhost:8080/shoppingonline/admin/dist/index.html
```

## 已修改的配置文件

以下文件已经正确配置：

1. **后端配置**：
   - `src/main/resources/application.yml` - context-path: /shoppingonline

2. **前端环境配置**：
   - `src/main/resources/admin/admin/.env` - VUE_APP_PROJECT_NAME=shoppingonline
   - `src/main/resources/admin/admin/src/utils/http.js` - baseURL: '/shoppingonline'
   - `src/main/resources/admin/admin/src/utils/base.js` - 项目名已更新

3. **前端页面配置**：
   - `src/main/resources/front/front/js/env-config.js` - PROJECT_NAME: 'shoppingonline'
   - `src/main/resources/front/front/modules/http/http.js` - 默认URL已更新

## 验证步骤

1. **确保Spring Boot应用已启动**
2. **重新构建Vue管理后台**（使用上述脚本）
3. **清除浏览器缓存**
4. **访问管理后台登录页面**
5. **检查浏览器开发者工具的Network标签**，确认请求URL为：
   ```
   POST http://localhost:8080/shoppingonline/yonghu/login
   ```

## 故障排除

如果仍然有问题：

1. **检查环境变量**：确认 `.env` 文件中的配置正确
2. **清除构建缓存**：删除 `dist` 目录后重新构建
3. **检查Node.js版本**：确保使用兼容的Node.js版本
4. **查看构建日志**：检查构建过程中是否有错误

## 访问地址总结

修复后的访问地址：
- **前端首页**：`http://localhost:8080/shoppingonline/front/index.html`
- **用户登录**：`http://localhost:8080/shoppingonline/front/pages/login/login.html`
- **管理后台**：`http://localhost:8080/shoppingonline/admin/dist/index.html`
- **API接口**：`http://localhost:8080/shoppingonline/`

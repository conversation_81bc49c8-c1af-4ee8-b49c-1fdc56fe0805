# Spring Boot 项目升级指南

## 升级概览

### 版本升级对比

| 组件 | 原版本 | 新版本 | 说明 |
|------|--------|--------|------|
| Spring Boot | 2.2.2.RELEASE | 2.7.18 | LTS版本，稳定可靠 |
| JDK | 1.8 | 11 | LTS版本，性能提升 |
| MyBatis Plus | 2.3 | 3.5.4.1 | 重大版本升级 |
| FastJSON | 1.2.8 | 2.0.43 | 修复安全漏洞 |
| Apache Shiro | 1.3.2 | 1.13.0 | 安全更新 |
| Swagger | SpringFox 2.9.2 | SpringDoc 1.7.0 | 新一代API文档 |
| Commons Lang3 | 3.0 | 3.14.0 | 功能增强 |
| Commons IO | 2.5 | 2.15.1 | 安全更新 |
| Hutool | 4.0.12 | 5.8.25 | 重大版本升级 |

## 升级步骤

### 1. 环境准备

#### 安装JDK 11
```bash
# 下载并安装JDK 11
# 设置JAVA_HOME环境变量
export JAVA_HOME=/path/to/jdk11
export PATH=$JAVA_HOME/bin:$PATH

# 验证安装
java -version
```

#### 更新IDE配置
- IntelliJ IDEA: File → Project Structure → Project → Project SDK → 选择JDK 11
- Eclipse: Project → Properties → Java Build Path → Libraries → Modulepath/Classpath → 选择JDK 11

### 2. 代码兼容性修改

#### MyBatis Plus 配置更新
原配置类已自动更新，主要变化：
- `PaginationInterceptor` → `MybatisPlusInterceptor`
- 新增内部拦截器机制

#### Swagger 配置更新
- 移除 `@EnableSwagger2` 注解
- 使用 `OpenAPI` 替代 `Docket`
- 新的访问地址：`/swagger-ui.html`

### 3. 可能需要手动修改的代码

#### 实体类注解更新
如果使用了MyBatis Plus的注解，可能需要更新：

```java
// 旧版本
@TableId(type = IdType.AUTO)
private Long id;

// 新版本（如果需要）
@TableId(type = IdType.AUTO)
private Long id;
```

#### 分页查询更新
```java
// 旧版本
Page<Entity> page = new Page<>(current, size);
IPage<Entity> result = mapper.selectPage(page, wrapper);

// 新版本（基本兼容，但建议使用新API）
Page<Entity> page = new Page<>(current, size);
IPage<Entity> result = mapper.selectPage(page, wrapper);
```

### 4. 构建和测试

#### 清理和重新构建
```bash
# 清理Maven缓存
mvn clean

# 重新编译
mvn compile

# 运行测试
mvn test

# 打包
mvn package
```

#### 启动应用
```bash
# 使用新的启动脚本
./start.bat

# 或手动启动
mvn spring-boot:run
```

### 5. 验证升级结果

#### 检查应用启动
- 确认应用正常启动
- 检查日志中是否有错误或警告

#### 测试功能
- 前端页面访问：`http://localhost:8080/shoppingonline/front/index.html`
- 管理后台：`http://localhost:8080/shoppingonline/admin/dist/index.html`
- API文档：`http://localhost:8080/shoppingonline/swagger-ui.html`

#### 数据库连接测试
- 确认数据库连接正常
- 测试CRUD操作
- 验证分页功能

## 潜在问题和解决方案

### 1. JDK 11 兼容性问题

#### 模块系统
如果遇到模块相关错误，在启动参数中添加：
```bash
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
```

#### 反射访问
某些反射操作可能需要额外配置：
```bash
--illegal-access=permit
```

### 2. MyBatis Plus 升级问题

#### 配置属性变化
- `refresh-mapper` 属性已移除
- `sql-injector` 配置方式改变

#### 代码生成器
如果使用代码生成器，需要更新到新版本API。

### 3. FastJSON 升级问题

#### API变化
FastJSON 2.x 有一些API变化，如果直接使用FastJSON API，可能需要调整。

### 4. Swagger 升级问题

#### 注解变化
- `@Api` → `@Tag`
- `@ApiOperation` → `@Operation`
- `@ApiParam` → `@Parameter`

## 性能优化建议

### 1. JVM 参数优化
```bash
-Xms1g -Xmx2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
```

### 2. Spring Boot 配置优化
```yaml
spring:
  jpa:
    hibernate:
      ddl-auto: none
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
```

## 回滚方案

如果升级后出现问题，可以：

1. **恢复原pom.xml**：使用Git回滚到升级前的版本
2. **恢复配置文件**：还原application.yml和配置类
3. **切换JDK版本**：重新设置为JDK 8

## 后续维护

### 定期更新
- 每季度检查依赖更新
- 关注安全漏洞公告
- 及时应用补丁版本

### 监控和日志
- 配置应用监控
- 设置日志级别
- 定期检查性能指标

## 联系支持

如果在升级过程中遇到问题：
1. 查看详细错误日志
2. 参考官方文档
3. 搜索相关问题解决方案
4. 必要时寻求技术支持

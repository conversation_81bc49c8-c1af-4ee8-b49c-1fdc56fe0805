@echo off
echo ========================================
echo 重新构建Vue管理后台
echo ========================================
echo.

echo 当前目录: %CD%
echo.

echo 进入Vue项目目录...
cd src\main\resources\admin\admin

echo 检查Node.js是否已安装...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [错误] Node.js未找到！请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 检查npm是否可用...
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [错误] npm未找到！请确保npm已正确安装
    pause
    exit /b 1
)

echo [信息] Node.js和npm检查通过
echo.

echo 显示当前环境变量配置...
echo VUE_APP_API_HOST=localhost
echo VUE_APP_API_PORT=8080  
echo VUE_APP_PROJECT_NAME=shoppingonline
echo.

echo 安装依赖包...
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 开始构建Vue应用...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 管理后台现在应该使用正确的API路径了
echo 访问地址: http://localhost:8080/shoppingonline/admin/dist/index.html
echo.
echo 如果仍有问题，请：
echo 1. 确保Spring Boot应用已启动
echo 2. 检查浏览器开发者工具的Network标签
echo 3. 清除浏览器缓存后重试
echo.

cd ..\..\..\..\..\

pause

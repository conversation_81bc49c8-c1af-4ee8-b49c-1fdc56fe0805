#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 174063616 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3550), pid=12656, tid=12404
#
# JRE version: Java(TM) SE Runtime Environment (17.0.6+9) (build 17.0.6+9-LTS-190)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190, mixed mode, emulated-client, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:D:\Java\IntelliJ IDEA 2024.3.4.1\lib\idea_rt.jar=56420 -Dfile.encoding=UTF-8 com.SpringbootSchemaApplication

Host: 11th Gen Intel(R) Core(TM) i5-11260H @ 2.60GHz, 12 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Fri Jul  4 21:53:28 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 14.899050 seconds (0d 0h 0m 14s)

---------------  T H R E A D  ---------------

Current thread (0x00000176a8379810):  VMThread "VM Thread" [stack: 0x0000004558600000,0x0000004558700000] [id=12404]

Stack: [0x0000004558600000,0x0000004558700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6734ea]
V  [jvm.dll+0x7d18f4]
V  [jvm.dll+0x7d309e]
V  [jvm.dll+0x7d3703]
V  [jvm.dll+0x2433c5]
V  [jvm.dll+0x6703f9]
V  [jvm.dll+0x664d32]
V  [jvm.dll+0x300086]
V  [jvm.dll+0x307606]
V  [jvm.dll+0x356c1e]
V  [jvm.dll+0x356e4f]
V  [jvm.dll+0x2d72e8]
V  [jvm.dll+0x2d57e5]
V  [jvm.dll+0x2d4dec]
V  [jvm.dll+0x31837b]
V  [jvm.dll+0x7d7f0b]
V  [jvm.dll+0x7d8c44]
V  [jvm.dll+0x7d915d]
V  [jvm.dll+0x7d9534]
V  [jvm.dll+0x7d9600]
V  [jvm.dll+0x781a7a]
V  [jvm.dll+0x672375]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]

VM_Operation (0x00000045580fd720): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000017691512a20


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000176aa2abf10, length=18, elements={
0x0000017691512a20, 0x00000176a837fe20, 0x00000176a8381540, 0x00000176a8391000,
0x00000176a8393ac0, 0x00000176a8394fe0, 0x00000176a8397070, 0x00000176a8397a20,
0x00000176a83abfc0, 0x00000176a8423db0, 0x00000176a912b2d0, 0x00000176a9156a00,
0x00000176a929d4e0, 0x00000176a9416d90, 0x00000176a94215f0, 0x00000176a94034a0,
0x00000176a9d91af0, 0x00000176a95e58c0
}

Java Threads: ( => current thread )
  0x0000017691512a20 JavaThread "main" [_thread_blocked, id=34232, stack(0x0000004558000000,0x0000004558100000)]
  0x00000176a837fe20 JavaThread "Reference Handler" daemon [_thread_blocked, id=11592, stack(0x0000004558700000,0x0000004558800000)]
  0x00000176a8381540 JavaThread "Finalizer" daemon [_thread_blocked, id=28592, stack(0x0000004558800000,0x0000004558900000)]
  0x00000176a8391000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=8784, stack(0x0000004558900000,0x0000004558a00000)]
  0x00000176a8393ac0 JavaThread "Attach Listener" daemon [_thread_blocked, id=13676, stack(0x0000004558a00000,0x0000004558b00000)]
  0x00000176a8394fe0 JavaThread "Service Thread" daemon [_thread_blocked, id=11688, stack(0x0000004558b00000,0x0000004558c00000)]
  0x00000176a8397070 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=13744, stack(0x0000004558c00000,0x0000004558d00000)]
  0x00000176a8397a20 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=18476, stack(0x0000004558d00000,0x0000004558e00000)]
  0x00000176a83abfc0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=22660, stack(0x0000004558e00000,0x0000004558f00000)]
  0x00000176a8423db0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=24540, stack(0x0000004558f00000,0x0000004559000000)]
  0x00000176a912b2d0 JavaThread "Monitor Ctrl-Break" daemon [_thread_in_native, id=28780, stack(0x0000004559300000,0x0000004559400000)]
  0x00000176a9156a00 JavaThread "Notification Thread" daemon [_thread_blocked, id=14832, stack(0x0000004559400000,0x0000004559500000)]
  0x00000176a929d4e0 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=21348, stack(0x0000004559500000,0x0000004559600000)]
  0x00000176a9416d90 JavaThread "RMI TCP Connection(1)-192.168.175.3" daemon [_thread_blocked, id=19540, stack(0x0000004559600000,0x0000004559700000)]
  0x00000176a94215f0 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=25864, stack(0x0000004559800000,0x0000004559900000)]
  0x00000176a94034a0 JavaThread "JMX server connection timeout 20" daemon [_thread_blocked, id=22664, stack(0x0000004559900000,0x0000004559a00000)]
  0x00000176a9d91af0 JavaThread "RMI TCP Connection(3)-192.168.175.3" daemon [_thread_blocked, id=2164, stack(0x0000004559a00000,0x0000004559b00000)]
  0x00000176a95e58c0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=10828, stack(0x0000004559100000,0x0000004559200000)]

Other Threads:
=>0x00000176a8379810 VMThread "VM Thread" [stack: 0x0000004558600000,0x0000004558700000] [id=12404]
  0x0000017691598960 WatcherThread [stack: 0x0000004559700000,0x0000004559800000] [id=5700]
  0x000001769157a920 GCTaskThread "GC Thread#0" [stack: 0x0000004558100000,0x0000004558200000] [id=37124]
  0x00000176a97cf800 GCTaskThread "GC Thread#1" [stack: 0x0000004559b00000,0x0000004559c00000] [id=20648]
  0x00000176a97b1000 GCTaskThread "GC Thread#2" [stack: 0x0000004559c00000,0x0000004559d00000] [id=30504]
  0x00000176a97c7ab0 GCTaskThread "GC Thread#3" [stack: 0x0000004559d00000,0x0000004559e00000] [id=32736]
  0x00000176a97c7d60 GCTaskThread "GC Thread#4" [stack: 0x0000004559e00000,0x0000004559f00000] [id=25340]
  0x00000176a97c8010 GCTaskThread "GC Thread#5" [stack: 0x0000004559f00000,0x000000455a000000] [id=18116]
  0x00000176a96d49d0 GCTaskThread "GC Thread#6" [stack: 0x000000455a000000,0x000000455a100000] [id=36332]
  0x00000176a96d4c80 GCTaskThread "GC Thread#7" [stack: 0x000000455a100000,0x000000455a200000] [id=3212]
  0x00000176a96d5fe0 GCTaskThread "GC Thread#8" [stack: 0x000000455a200000,0x000000455a300000] [id=14488]
  0x00000176a96d64a0 GCTaskThread "GC Thread#9" [stack: 0x000000455a300000,0x000000455a400000] [id=34136]
  0x000001769158c1f0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000004558200000,0x0000004558300000] [id=2464]
  0x000001769158cc00 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000004558300000,0x0000004558400000] [id=8380]
  0x00000176a79a65c0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000004558400000,0x0000004558500000] [id=18320]
  0x00000176a96f0fe0 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000455a400000,0x000000455a500000] [id=26436]
  0x00000176a96f0460 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000455a500000,0x000000455a600000] [id=13324]
  0x00000176915c8fa0 ConcurrentGCThread "G1 Service" [stack: 0x0000004558500000,0x0000004558600000] [id=13568]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001768f224900] Threads_lock - owner thread: 0x00000176a8379810
[0x0000017691505730] Heap_lock - owner thread: 0x0000017691512a20

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=1
LinkageErrors=41

Heap address: 0x0000000703a00000, size: 4038 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bd0000-0x0000000800bd0000), size 12386304, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 16150M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4038M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 12987K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|   1|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703c00000, 0x0000000703c00000| Untracked 
|   2|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|   3|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|   4|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|   5|0x0000000704400000, 0x0000000704600000, 0x0000000704600000|100%| O|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|   6|0x0000000704600000, 0x000000070465d400, 0x0000000704800000| 18%| O|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|   7|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|   8|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|   9|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  10|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  11|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  12|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  13|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  14|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  15|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  16|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  17|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  18|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  19|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000, 0x0000000706000000| Untracked 
|  20|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000, 0x0000000706200000| Untracked 
|  21|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  22|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  23|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  24|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  25|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  26|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Untracked 
|  27|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000, 0x0000000707000000| Untracked 
|  28|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000, 0x0000000707200000| Untracked 
|  29|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000, 0x0000000707400000| Untracked 
|  30|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000, 0x0000000707600000| Untracked 
|  31|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000, 0x0000000707800000| Untracked 
|  32|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000, 0x0000000707a00000| Untracked 
|  33|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000, 0x0000000707c00000| Untracked 
|  34|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000, 0x0000000707e00000| Untracked 
|  35|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000, 0x0000000708000000| Untracked 
|  36|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000, 0x0000000708200000| Untracked 
|  37|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000, 0x0000000708400000| Untracked 
|  38|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000, 0x0000000708600000| Untracked 
|  39|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000, 0x0000000708800000| Untracked 
|  40|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000, 0x0000000708a00000| Untracked 
|  41|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000, 0x0000000708c00000| Untracked 
|  42|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000, 0x0000000708e00000| Untracked 
|  43|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000, 0x0000000709000000| Untracked 
|  44|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000, 0x0000000709200000| Untracked 
|  45|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000, 0x0000000709400000| Untracked 
|  46|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000, 0x0000000709600000| Untracked 
|  47|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000, 0x0000000709800000| Untracked 
|  48|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000, 0x0000000709a00000| Untracked 
|  49|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000, 0x0000000709c00000| Untracked 
|  50|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000, 0x0000000709e00000| Untracked 
|  51|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000, 0x000000070a000000| Untracked 
|  52|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000, 0x000000070a200000| Untracked 
|  53|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000, 0x000000070a400000| Untracked 
|  54|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000, 0x000000070a600000| Untracked 
|  55|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000, 0x000000070a800000| Untracked 
|  56|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000, 0x000000070aa00000| Untracked 
|  57|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000, 0x000000070ac00000| Untracked 
|  58|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000, 0x000000070ae00000| Untracked 
|  59|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000, 0x000000070b000000| Untracked 
|  60|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000, 0x000000070b200000| Untracked 
|  61|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000, 0x000000070b400000| Untracked 
|  62|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000, 0x000000070b600000| Untracked 
|  63|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000, 0x000000070b800000| Untracked 
|  64|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000, 0x000000070ba00000| Untracked 
|  65|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000, 0x000000070bc00000| Untracked 
|  66|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000, 0x000000070be00000| Untracked 
|  67|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  68|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  69|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  70|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
|  71|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
|  72|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000, 0x000000070ca00000| Untracked 
|  73|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000, 0x000000070cc00000| Untracked 
|  74|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000, 0x000000070ce00000| Untracked 
|  75|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000, 0x000000070d000000| Untracked 
|  76|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000, 0x000000070d200000| Untracked 
|  77|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000, 0x000000070d400000| Untracked 
|  78|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000, 0x000000070d600000| Untracked 
|  79|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000, 0x000000070d800000| Untracked 
|  80|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000, 0x000000070da00000| Untracked 
|  81|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000, 0x000000070dc00000| Untracked 
|  82|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000, 0x000000070de00000| Untracked 
|  83|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000, 0x000000070e000000| Untracked 
|  84|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000, 0x000000070e200000| Untracked 
|  85|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000, 0x000000070e400000| Untracked 
|  86|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000, 0x000000070e600000| Untracked 
|  87|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000, 0x000000070e800000| Untracked 
|  88|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000, 0x000000070ea00000| Untracked 
|  89|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000, 0x000000070ec00000| Untracked 
|  90|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000, 0x000000070ee00000| Untracked 
|  91|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000, 0x000000070f000000| Untracked 
|  92|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000, 0x000000070f200000| Untracked 
|  93|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000, 0x000000070f400000| Untracked 
|  94|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000, 0x000000070f600000| Untracked 
|  95|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000, 0x000000070f800000| Untracked 
|  96|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000, 0x000000070fa00000| Untracked 
|  97|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Untracked 
|  98|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000, 0x000000070fe00000| Untracked 
|  99|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000, 0x0000000710000000| Untracked 
| 100|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000, 0x0000000710200000| Untracked 
| 101|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000, 0x0000000710400000| Untracked 
| 102|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000, 0x0000000710600000| Untracked 
| 103|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000, 0x0000000710800000| Untracked 
| 104|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000, 0x0000000710a00000| Untracked 
| 105|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000, 0x0000000710c00000| Untracked 
| 106|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000, 0x0000000710e00000| Untracked 
| 107|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000, 0x0000000711000000| Untracked 
| 108|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000, 0x0000000711200000| Untracked 
| 109|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000, 0x0000000711400000| Untracked 
| 110|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000, 0x0000000711600000| Untracked 
| 111|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000, 0x0000000711800000| Untracked 
| 112|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000, 0x0000000711a00000| Untracked 
| 113|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000, 0x0000000711c00000| Untracked 
| 114|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000, 0x0000000711e00000| Untracked 
| 115|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000, 0x0000000712000000| Untracked 
| 116|0x0000000712200000, 0x0000000712200000, 0x0000000712400000|  0%| F|  |TAMS 0x0000000712200000, 0x0000000712200000| Untracked 
| 117|0x0000000712400000, 0x0000000712400000, 0x0000000712600000|  0%| F|  |TAMS 0x0000000712400000, 0x0000000712400000| Untracked 
| 118|0x0000000712600000, 0x0000000712600000, 0x0000000712800000|  0%| F|  |TAMS 0x0000000712600000, 0x0000000712600000| Untracked 
| 119|0x0000000712800000, 0x0000000712800000, 0x0000000712a00000|  0%| F|  |TAMS 0x0000000712800000, 0x0000000712800000| Untracked 
| 120|0x0000000712a00000, 0x0000000712a00000, 0x0000000712c00000|  0%| F|  |TAMS 0x0000000712a00000, 0x0000000712a00000| Untracked 
| 121|0x0000000712c00000, 0x0000000712c51ab0, 0x0000000712e00000| 15%| S|CS|TAMS 0x0000000712c00000, 0x0000000712c00000| Complete 
| 122|0x0000000712e00000, 0x0000000712e00000, 0x0000000713000000|  0%| F|  |TAMS 0x0000000712e00000, 0x0000000712e00000| Untracked 
| 123|0x0000000713000000, 0x0000000713000000, 0x0000000713200000|  0%| F|  |TAMS 0x0000000713000000, 0x0000000713000000| Untracked 
| 124|0x0000000713200000, 0x0000000713200000, 0x0000000713400000|  0%| F|  |TAMS 0x0000000713200000, 0x0000000713200000| Untracked 
| 125|0x0000000713400000, 0x0000000713400000, 0x0000000713600000|  0%| F|  |TAMS 0x0000000713400000, 0x0000000713400000| Untracked 
| 126|0x0000000713600000, 0x0000000713600000, 0x0000000713800000|  0%| F|  |TAMS 0x0000000713600000, 0x0000000713600000| Untracked 

Card table byte_map: [0x000001769c8a0000,0x000001769d090000] _byte_map_base: 0x0000017699083000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001769157af30, (CMBitMap*) 0x000001769157af70
 Prev Bits: [0x000001769d880000, 0x00000176a1798000)
 Next Bits: [0x00000176a17a0000, 0x00000176a56b8000)

Polling page: 0x000001768f180000

Metaspace:

Usage:
  Non-class:     16.18 MB used.
      Class:      2.59 MB used.
       Both:     18.76 MB used.

Virtual space:
  Non-class space:       24.00 MB reserved,      16.31 MB ( 68%) committed,  3 nodes.
      Class space:        1.00 GB reserved,       2.69 MB ( <1%) committed,  1 nodes.
             Both:        1.02 GB reserved,      19.00 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  3.52 MB
       Class:  1.32 MB
        Both:  4.84 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 304.
num_arena_deaths: 0.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 304.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 908.
num_chunk_merges: 0.
num_chunk_splits: 594.
num_chunks_enlarged: 429.
num_purges: 0.
num_inconsistent_stats: 0.

CodeCache: size=49152Kb used=5926Kb max_used=6888Kb free=43225Kb
 bounds [0x0000017698d80000, 0x0000017699440000, 0x000001769bd80000]
 total_blobs=3167 nmethods=2685 adapters=408
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 12.800 Thread 0x00000176a8397a20 nmethod 2679 0x0000017699028d90 code [0x0000017699028fa0, 0x00000176990295f8]
Event: 12.800 Thread 0x00000176a8397a20 2680       1       java.lang.Math::min (12 bytes)
Event: 12.800 Thread 0x00000176a8397a20 nmethod 2680 0x0000017699028a90 code [0x0000017699028c20, 0x0000017699028cd8]
Event: 12.800 Thread 0x00000176a95e58c0 nmethod 2678 0x0000017699028590 code [0x0000017699028740, 0x0000017699028938]
Event: 12.800 Thread 0x00000176a8397a20 2681       1       java.io.ObjectInputStream$HandleTable::assign (48 bytes)
Event: 12.800 Thread 0x00000176a95e58c0 2682   !   1       java.io.ObjectInputStream::readObject0 (681 bytes)
Event: 12.800 Thread 0x00000176a8397a20 nmethod 2681 0x0000017699028010 code [0x00000176990281c0, 0x0000017699028408]
Event: 12.800 Thread 0x00000176a8397a20 2683       1       java.io.ObjectInputStream$PeekInputStream::getBytesRead (5 bytes)
Event: 12.800 Thread 0x00000176a8397a20 nmethod 2683 0x0000017699027d10 code [0x0000017699027ea0, 0x0000017699027f78]
Event: 12.800 Thread 0x00000176a8397a20 2684       1       jdk.internal.event.DeserializationEvent::shouldCommit (2 bytes)
Event: 12.800 Thread 0x00000176a8397a20 nmethod 2684 0x0000017699027a10 code [0x0000017699027ba0, 0x0000017699027c78]
Event: 12.800 Thread 0x00000176a8397a20 2685       1       java.io.Bits::getLong (105 bytes)
Event: 12.801 Thread 0x00000176a8397a20 nmethod 2685 0x0000017699027610 code [0x00000176990277a0, 0x0000017699027978]
Event: 12.801 Thread 0x00000176a8397a20 2686       1       sun.util.logging.internal.LoggingProviderImpl$JULWrapper::isLoggable (12 bytes)
Event: 12.801 Thread 0x00000176a8397a20 nmethod 2686 0x0000017699027210 code [0x00000176990273a0, 0x00000176990274c8]
Event: 12.802 Thread 0x00000176a95e58c0 nmethod 2682 0x0000017699023a10 code [0x0000017699023fe0, 0x0000017699025ac8]
Event: 12.842 Thread 0x00000176a8397a20 2687   !   1       java.io.ObjectOutputStream::writeOrdinaryObject (196 bytes)
Event: 12.842 Thread 0x00000176a95e58c0 2688       1       java.io.ObjectStreamClass::checkSerialize (20 bytes)
Event: 12.842 Thread 0x00000176a95e58c0 nmethod 2688 0x0000017699023510 code [0x00000176990236c0, 0x00000176990238b8]
Event: 12.842 Thread 0x00000176a8397a20 nmethod 2687 0x0000017699022490 code [0x0000017699022700, 0x0000017699022de8]

GC Heap History (13 events):
Event: 0.420 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 260096K, used 22528K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 11 young (22528K), 0 survivors (0K)
 Metaspace       used 7544K, committed 7744K, reserved 1056768K
  class space    used 965K, committed 1088K, reserved 1048576K
}
Event: 0.424 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 6817K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 7544K, committed 7744K, reserved 1056768K
  class space    used 965K, committed 1088K, reserved 1048576K
}
Event: 0.693 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 29345K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 2 survivors (4096K)
 Metaspace       used 13446K, committed 13696K, reserved 1064960K
  class space    used 1897K, committed 1984K, reserved 1048576K
}
Event: 0.696 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 8928K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 13446K, committed 13696K, reserved 1064960K
  class space    used 1897K, committed 1984K, reserved 1048576K
}
Event: 1.269 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 107232K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 50 young (102400K), 1 survivors (2048K)
 Metaspace       used 19050K, committed 19328K, reserved 1073152K
  class space    used 2619K, committed 2752K, reserved 1048576K
}
Event: 1.273 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 12098K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 19050K, committed 19328K, reserved 1073152K
  class space    used 2619K, committed 2752K, reserved 1048576K
}
Event: 12.039 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 161602K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 76 young (155648K), 3 survivors (6144K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}
Event: 12.798 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 260096K, used 13989K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 4 survivors (8192K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}
Event: 12.812 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 260096K, used 16037K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 4 survivors (8192K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}
Event: 12.815 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 260096K, used 13850K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}
Event: 12.853 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 260096K, used 24090K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 1 survivors (2048K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}
Event: 12.854 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 260096K, used 12985K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}
Event: 12.886 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 260096K, used 23225K [0x0000000703a00000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 1 survivors (2048K)
 Metaspace       used 19213K, committed 19456K, reserved 1073152K
  class space    used 2648K, committed 2752K, reserved 1048576K
}

Deoptimization events (2 events):
Event: 0.789 Thread 0x0000017691512a20 DEOPT PACKING pc=0x0000017699338379 sp=0x00000045580fd6e0
Event: 0.789 Thread 0x0000017691512a20 DEOPT UNPACKING pc=0x0000017698dd66e3 sp=0x00000045580fcbc0 mode 3

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.867 Thread 0x0000017691512a20 Exception <a 'java/lang/ClassNotFoundException'{0x000000071202f9d0}: org/springframework/beans/factory/AwareCustomizer> (0x000000071202f9d0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 0.867 Thread 0x0000017691512a20 Exception <a 'java/lang/ClassNotFoundException'{0x0000000712032600}: org/springframework/context/EnvironmentAwareCustomizer> (0x0000000712032600) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 0.867 Thread 0x0000017691512a20 Exception <a 'java/lang/ClassNotFoundException'{0x0000000712036418}: org/springframework/beans/factory/AwareCustomizer> (0x0000000712036418) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 0.867 Thread 0x00000176a978b5f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000712339be8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000712339be8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.867 Thread 0x00000176a978b5f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071233d1e8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x000000071233d1e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.869 Thread 0x00000176a978b5f0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071234ad10}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000071234ad10) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 0.869 Thread 0x00000176a978b5f0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000071234e2a0}: Found class java.lang.Object, but interface was expected> (0x000000071234e2a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 0.876 Thread 0x00000176a978b5f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007123b5ca0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007123b5ca0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 1.031 Thread 0x00000176a9416d90 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000713253220}> (0x0000000713253220) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 1.035 Thread 0x00000176a9416d90 Exception <a 'java/lang/NoSuchMethodError'{0x000000071327ce60}: static [Ljava/lang/StackTraceElement;.<clinit>()V> (0x000000071327ce60) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 1107]
Event: 2.374 Thread 0x00000176a9416d90 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000710d0a130}> (0x0000000710d0a130) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 3.413 Thread 0x00000176a9416d90 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000710d16920}> (0x0000000710d16920) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 3.922 Thread 0x00000176a9416d90 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000710d22e40}> (0x0000000710d22e40) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 4.429 Thread 0x00000176a9416d90 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000710d2f3b0}> (0x0000000710d2f3b0) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 5.468 Thread 0x00000176a9d91af0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x000000071071ae38}> (0x000000071071ae38) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 6.978 Thread 0x00000176a9d91af0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x00000007107272b8}> (0x00000007107272b8) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 8.501 Thread 0x00000176a9d91af0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000710733788}> (0x0000000710733788) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 11.530 Thread 0x00000176a9d91af0 Exception <a 'java/lang/OutOfMemoryError'{0x0000000710738770}: unable to create native thread: possibly out of memory or process/resource limits reached> (0x0000000710738770) 
thrown [s\open\src\hotspot\share\prims\jvm.cpp, line 2923]
Event: 11.530 Thread 0x00000176a9d91af0 Exception <a 'java/lang/reflect/InvocationTargetException'{0x0000000710738a40}> (0x0000000710738a40) 
thrown [s\open\src\hotspot\share\runtime\reflection.cpp, line 1121]
Event: 11.734 Thread 0x0000017691512a20 Exception <a 'java/lang/NoSuchMethodError'{0x000000070afaf050}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070afaf050) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 0.832 Executing VM operation: HandshakeAllThreads done
Event: 0.878 Executing VM operation: HandshakeAllThreads
Event: 0.878 Executing VM operation: HandshakeAllThreads done
Event: 1.269 Executing VM operation: G1CollectForAllocation
Event: 1.273 Executing VM operation: G1CollectForAllocation done
Event: 3.300 Executing VM operation: Cleanup
Event: 3.410 Executing VM operation: Cleanup done
Event: 4.421 Executing VM operation: Cleanup
Event: 4.421 Executing VM operation: Cleanup done
Event: 5.433 Executing VM operation: Cleanup
Event: 5.465 Executing VM operation: Cleanup done
Event: 7.471 Executing VM operation: Cleanup
Event: 7.471 Executing VM operation: Cleanup done
Event: 12.039 Executing VM operation: G1CollectForAllocation
Event: 12.798 Executing VM operation: G1CollectForAllocation done
Event: 12.812 Executing VM operation: G1CollectForAllocation
Event: 12.815 Executing VM operation: G1CollectForAllocation done
Event: 12.853 Executing VM operation: G1CollectForAllocation
Event: 12.854 Executing VM operation: G1CollectForAllocation done
Event: 12.886 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 3.410 Thread 0x00000176a9d91fc0 Thread added: 0x00000176a9d91fc0
Event: 3.411 Thread 0x00000176a849ffd0 Thread exited: 0x00000176a849ffd0
Event: 3.414 Thread 0x00000176a9d91fc0 Thread exited: 0x00000176a9d91fc0
Event: 3.916 Thread 0x00000176a849fac0 Thread exited: 0x00000176a849fac0
Event: 3.916 Thread 0x00000176a849f5b0 Thread exited: 0x00000176a849f5b0
Event: 3.917 Thread 0x00000176a9d92490 Thread added: 0x00000176a9d92490
Event: 3.925 Thread 0x00000176a9d92490 Thread exited: 0x00000176a9d92490
Event: 4.426 Thread 0x00000176a9d91fc0 Thread added: 0x00000176a9d91fc0
Event: 4.430 Thread 0x00000176a9d91fc0 Thread exited: 0x00000176a9d91fc0
Event: 5.464 Thread 0x00000176a9d91fc0 Thread added: 0x00000176a9d91fc0
Event: 6.471 Thread 0x00000176a9d91fc0 Thread exited: 0x00000176a9d91fc0
Event: 6.974 Thread 0x00000176a9d91fc0 Thread added: 0x00000176a9d91fc0
Event: 6.980 Thread 0x00000176a9d91fc0 Thread exited: 0x00000176a9d91fc0
Event: 8.497 Thread 0x00000176a9d91fc0 Thread added: 0x00000176a9d91fc0
Event: 8.502 Thread 0x00000176a9d91fc0 Thread exited: 0x00000176a9d91fc0
Event: 11.530 loading class java/rmi/ServerError
Event: 11.530 loading class java/rmi/ServerError done
Event: 12.038 Thread 0x00000176a9d91fc0 Thread added: 0x00000176a9d91fc0
Event: 12.799 Thread 0x00000176a95e58c0 Thread added: 0x00000176a95e58c0
Event: 12.802 Thread 0x00000176a9d91fc0 Thread exited: 0x00000176a9d91fc0


Dynamic libraries:
0x00007ff614e70000 - 0x00007ff614e80000 	D:\Java\bin\java.exe
0x00007ff852cc0000 - 0x00007ff852f25000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8513b0000 - 0x00007ff851479000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8502e0000 - 0x00007ff8506c8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8500f0000 - 0x00007ff85023b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff838bf0000 - 0x00007ff838c08000 	D:\Java\bin\jli.dll
0x00007ff8320e0000 - 0x00007ff8320fb000 	D:\Java\bin\VCRUNTIME140.dll
0x00007ff851ff0000 - 0x00007ff8520a3000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff850c40000 - 0x00007ff850ce9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff851d80000 - 0x00007ff851e26000 	C:\WINDOWS\System32\sechost.dll
0x00007ff851080000 - 0x00007ff851195000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff852890000 - 0x00007ff852a5a000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8366d0000 - 0x00007ff83696a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ff84fe10000 - 0x00007ff84fe37000 	C:\WINDOWS\System32\win32u.dll
0x00007ff851f30000 - 0x00007ff851f5b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff84fe40000 - 0x00007ff84ff77000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff850040000 - 0x00007ff8500e3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff844da0000 - 0x00007ff844dab000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff851d40000 - 0x00007ff851d70000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff844650000 - 0x00007ff84465c000 	D:\Java\bin\vcruntime140_1.dll
0x00007ff832050000 - 0x00007ff8320de000 	D:\Java\bin\msvcp140.dll
0x00007fffe6ee0000 - 0x00007fffe7ab7000 	D:\Java\bin\server\jvm.dll
0x00007ff852a60000 - 0x00007ff852a68000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff83d0e0000 - 0x00007ff83d0ea000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ff839580000 - 0x00007ff8395b5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff851f70000 - 0x00007ff851fe4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff84ecf0000 - 0x00007ff84ed0b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff83dd40000 - 0x00007ff83dd4a000 	D:\Java\bin\jimage.dll
0x00007ff84d400000 - 0x00007ff84d641000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff850cf0000 - 0x00007ff851075000 	C:\WINDOWS\System32\combase.dll
0x00007ff852b90000 - 0x00007ff852c71000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff82da00000 - 0x00007ff82da39000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff850240000 - 0x00007ff8502d9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff83c4f0000 - 0x00007ff83c4fe000 	D:\Java\bin\instrument.dll
0x00007ff831ab0000 - 0x00007ff831ad5000 	D:\Java\bin\java.dll
0x00007ff804450000 - 0x00007ff804527000 	D:\Java\bin\jsvml.dll
0x00007ff852140000 - 0x00007ff852882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff850850000 - 0x00007ff8509c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ff84dab0000 - 0x00007ff84e308000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff851480000 - 0x00007ff851571000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8517c0000 - 0x00007ff85182a000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff84fd20000 - 0x00007ff84fd4f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff831a70000 - 0x00007ff831a88000 	D:\Java\bin\zip.dll
0x00007ff831ce0000 - 0x00007ff831cf9000 	D:\Java\bin\net.dll
0x00007ff846750000 - 0x00007ff84686e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff84f260000 - 0x00007ff84f2ca000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff831a90000 - 0x00007ff831aa6000 	D:\Java\bin\nio.dll
0x00007ff84e780000 - 0x00007ff84e8a7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ff84e740000 - 0x00007ff84e773000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff851230000 - 0x00007ff85123a000 	C:\WINDOWS\System32\NSI.dll
0x00007ff8451b0000 - 0x00007ff8451bb000 	C:\Windows\System32\rasadhlp.dll
0x00007ff846df0000 - 0x00007ff846e76000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ff83c1f0000 - 0x00007ff83c1fa000 	D:\Java\bin\management.dll
0x00007ff837060000 - 0x00007ff83706b000 	D:\Java\bin\management_ext.dll
0x00007ff84f510000 - 0x00007ff84f52b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff84ec50000 - 0x00007ff84ec8a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff84f300000 - 0x00007ff84f32b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff84fcf0000 - 0x00007ff84fd16000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ff84f530000 - 0x00007ff84f53c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff846a50000 - 0x00007ff846a6f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ff846a20000 - 0x00007ff846a45000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ff8223a0000 - 0x00007ff8223b8000 	C:\WINDOWS\system32\napinsp.dll
0x00007ff821690000 - 0x00007ff8216a2000 	C:\WINDOWS\System32\winrnr.dll
0x00007ff8216d0000 - 0x00007ff821700000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ff821660000 - 0x00007ff821680000 	C:\WINDOWS\system32\wshbth.dll
0x00007ff8369b0000 - 0x00007ff8369c0000 	D:\Java\bin\verify.dll
0x00007ff835b00000 - 0x00007ff835b4e000 	C:\WINDOWS\SYSTEM32\pdh.dll
0x00007ff844970000 - 0x00007ff844982000 	C:\WINDOWS\System32\perfproc.dll
0x00007ff81fe70000 - 0x00007ff81fe81000 	C:\WINDOWS\System32\perfos.dll
0x00007ff84e330000 - 0x00007ff84e342000 	C:\WINDOWS\SYSTEM32\pfclient.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Java\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;D:\Java\bin\server

VM Arguments:
jvm_args: -XX:TieredStopAtLevel=1 -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:D:\Java\IntelliJ IDEA 2024.3.4.1\lib\idea_rt.jar=56420 -Dfile.encoding=UTF-8 
java_command: com.SpringbootSchemaApplication
java_class_path (initial): D:\springboot\springboot7z60r\target\classes;D:\maven换源\repository\org\springframework\boot\spring-boot-starter-web\2.2.2.RELEASE\spring-boot-starter-web-2.2.2.RELEASE.jar;D:\maven换源\repository\org\springframework\boot\spring-boot-starter\2.2.2.RELEASE\spring-boot-starter-2.2.2.RELEASE.jar;D:\maven换源\repository\org\springframework\boot\spring-boot\2.2.2.RELEASE\spring-boot-2.2.2.RELEASE.jar;D:\maven换源\repository\org\springframework\boot\spring-boot-starter-logging\2.2.2.RELEASE\spring-boot-starter-logging-2.2.2.RELEASE.jar;D:\maven换源\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\maven换源\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\maven换源\repository\org\apache\logging\log4j\log4j-to-slf4j\2.12.1\log4j-to-slf4j-2.12.1.jar;D:\maven换源\repository\org\apache\logging\log4j\log4j-api\2.12.1\log4j-api-2.12.1.jar;D:\maven换源\repository\org\slf4j\jul-to-slf4j\1.7.29\jul-to-slf4j-1.7.29.jar;D:\maven换源\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\maven换源\repository\org\yaml\snakeyaml\1.25\snakeyaml-1.25.jar;D:\maven换源\repository\org\springframework\boot\spring-boot-starter-json\2.2.2.RELEASE\spring-boot-starter-json-2.2.2.RELEASE.jar;D:\maven换源\repository\com\fasterxml\jackson\core\jackson-databind\2.10.1\jackson-databind-2.10.1.jar;D:\maven换源\repository\com\fasterxml\jackson\core\jackson-core\2.10.1\jackson-core-2.10.1.jar;D:\maven换源\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.10.1\jackson-datatype-jdk8-2.10.1.jar;D:\maven换源\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.10.1\jackson-datatype-jsr310-2.10.1.jar;D:\maven换源\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.10.1\jackson-module-parameter-names-2.10.1.jar;D:\maven换源\repository\org\springframework\boot\spring-boot-starter-tomcat\2.2.2.RELEASE\spring
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
     bool ManagementServer                         = true                                      {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4234149888                                {product} {ergonomic}
   size_t MaxNewSize                               = 2539651072                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 0                                      {pd product} {ergonomic}
     bool ProfileInterpreter                       = false                                  {pd product} {command line}
    uintx ProfiledCodeHeapSize                     = 0                                      {pd product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4234149888                             {manageable} {ergonomic}
     intx TieredStopAtLevel                        = 1                                         {product} {command line}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\Java
PATH=d:\cursor\resources\app\bin;C:\Program Files (x86)\Microsoft\Edge\Application;D:\VM\VMware\VMware Workstation Pro\bin\;D:\Java\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\mingw64\bin;D:\miniconda3\Scripts;D:\miniconda3;D:\miniconda3\Library\bin;C:\Program Files\MySQL\MySQL Server 8.0\bin;D:\python3.10.5\;D:\python3.10.5\Scripts\;C:\Users\<USER>\AppData\Roaming\Python\Python310\Scripts;C:;Users\胡俊杰\AppData\Roaming\Python\Python310;MinGW\bin;C:\GnuWin32\flex\bin;C:\GnuWin32\bison\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\apache-maven-3.9.9\bin;D:\Git\cmd;D:\nodejs\;D:\Tomcat 11.0\bin;D:\python3.10.5\Scripts\;D:\python3.10.5\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\桌面\PyCharm Community Edition 2022.1.2\bin;;C:\Us;D:\python3.10.5\Scripts\;D:\python3.10.5\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\桌面\PyCharm Community Edition 2022.1.2\bin;;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\Python\Python310\Scripts;C:\Users\<USER>\AppData\Roaming\Python\Python310;;D:\Java\IntelliJ IDEA 2024.3.4.1\bin;;C:\Users\<USER>\AppData\Roaming\npm;D:\cursor\resources\app\bin
USERNAME=胡俊杰
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 4 days 1:08 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x3c, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi

Memory: 4k page, system-wide physical 16150M (2881M free)
TotalPageFile size 19064M (AvailPageFile size 7M)
current process WorkingSet (physical memory assigned to process): 250M, peak: 275M
current process commit charge ("private bytes"): 391M, peak: 557M

vm_info: Java HotSpot(TM) 64-Bit Server VM (17.0.6+9-LTS-190) for windows-amd64 JRE (17.0.6+9-LTS-190), built on Dec  6 2022 15:53:54 by "mach5one" with MS VC++ 17.1 (VS2022)

END.
